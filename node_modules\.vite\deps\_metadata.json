{"hash": "dc8e54f4", "configHash": "5ba07d79", "lockfileHash": "fe62e07f", "browserHash": "4b9f6ef2", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0fbb6f2e", "needsInterop": true}, "@phosphor-icons/react": {"src": "../../@phosphor-icons/react/dist/index.es.js", "file": "@phosphor-icons_react.js", "fileHash": "119b5f5f", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "161ea2ab", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "45aa4c2c", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "757701b5", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "4eb6fd60", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "9716ea3b", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "d08d8ec0", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "9416ad58", "needsInterop": false}, "hls.js": {"src": "../../hls.js/dist/hls.mjs", "file": "hls__js.js", "fileHash": "b9e83792", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a40978e8", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "237d30a2", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c6ae0455", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "1dbc5a4d", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "0a8db053", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b02b37d1", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "64aefef7", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "66f59ce8", "needsInterop": false}, "video.js": {"src": "../../video.js/dist/video.es.js", "file": "video__js.js", "fileHash": "e8ac805d", "needsInterop": false}}, "chunks": {"chunk-OYX3GRGT": {"file": "chunk-OYX3GRGT.js"}, "chunk-J7P45GN4": {"file": "chunk-J7P45GN4.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-TQG5UYZM": {"file": "chunk-TQG5UYZM.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}