import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNTIsNTZWMTc2YTgsOCwwLDAsMS0xNiwwVjEyNEg0OHY1MmE4LDgsMCwwLDEtMTYsMFY1NmE4LDgsMCwwLDEsMTYsMHY1Mmg4OFY1NmE4LDgsMCwwLDEsMTYsMFptNjAsODhhMzguOCwzOC44LDAsMCwwLTkuNDEsMS4xNEwyMDYuNzgsMTIwSDI0MGE4LDgsMCwwLDAsMC0xNkgyMDBhOCw4LDAsMCwwLTcuODksNi42OGwtOCw0OGE4LDgsMCwwLDAsMTMuNiw2LjkyQTE5LjczLDE5LjczLDAsMCwxLDIxMiwxNjBhMjAsMjAsMCwwLDEsMCw0MCwxOS43MywxOS43MywwLDAsMS0xNC4yOS01LjYsOCw4LDAsMSwwLTExLjQyLDExLjJBMzUuNTQsMzUuNTQsMCwwLDAsMjEyLDIxNmEzNiwzNiwwLDAsMCwwLTcyWiIvPjwvc3ZnPg==)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDQsMTgwYTMyLDMyLDAsMCwxLTMyLDMyLDMxLjYyLDMxLjYyLDAsMCwxLTIyLjg2LTkuMiw0LDQsMCwwLDEsNS43Mi01LjZBMjMuNjcsMjMuNjcsMCwwLDAsMjEyLDIwNGEyNCwyNCwwLDAsMCwwLTQ4LDIzLjY3LDIzLjY3LDAsMCwwLTE3LjE0LDYuOCw0LDQsMCwwLDEtNi44MS0zLjQ2bDgtNDhBNCw0LDAsMCwxLDIwMCwxMDhoNDBhNCw0LDAsMCwxLDAsOEgyMDMuMzlsLTUuODcsMzUuMjFBMzMuMjYsMzMuMjYsMCwwLDEsMjEyLDE0OCwzMiwzMiwwLDAsMSwyNDQsMTgwWk0xNDQsNTJhNCw0LDAsMCwwLTQsNHY1Nkg0NFY1NmE0LDQsMCwwLDAtOCwwVjE3NmE0LDQsMCwwLDAsOCwwVjEyMGg5NnY1NmE0LDQsMCwwLDAsOCwwVjU2QTQsNCwwLDAsMCwxNDQsNTJaIi8+PC9zdmc+)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDYsMTgwYTM0LDM0LDAsMCwxLTM0LDM0LDMzLjYsMzMuNiwwLDAsMS0yNC4yOS05LjgsNiw2LDAsMCwxLDguNTgtOC40QTIxLjY1LDIxLjY1LDAsMCwwLDIxMiwyMDJhMjIsMjIsMCwwLDAsMC00NCwyMS42NSwyMS42NSwwLDAsMC0xNS43MSw2LjJBNiw2LDAsMCwxLDE4Ni4wOCwxNTlsOC00OGE2LDYsMCwwLDEsNS45Mi01aDQwYTYsNiwwLDAsMSwwLDEySDIwNS4wOGwtNSwzMEEzNiwzNiwwLDAsMSwyMTIsMTQ2LDM0LDM0LDAsMCwxLDI0NiwxODBaTTE0NCw1MGE2LDYsMCwwLDAtNiw2djU0SDQ2VjU2YTYsNiwwLDAsMC0xMiwwVjE3NmE2LDYsMCwwLDAsMTIsMFYxMjJoOTJ2NTRhNiw2LDAsMCwwLDEyLDBWNTZBNiw2LDAsMCwwLDE0NCw1MFoiLz48L3N2Zz4=)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNTIsMTgwYTQwLDQwLDAsMCwxLTQwLDQwLDM5LjUzLDM5LjUzLDAsMCwxLTI4LjU3LTExLjYsMTIsMTIsMCwxLDEsMTcuMTQtMTYuOEExNS41NCwxNS41NCwwLDAsMCwyMTIsMTk2YTE2LDE2LDAsMCwwLDAtMzIsMTUuNTQsMTUuNTQsMCwwLDAtMTEuNDMsNC40QTEyLDEyLDAsMCwxLDE4MC4xNiwxNThsOC00OEExMiwxMiwwLDAsMSwyMDAsMTAwaDQwYTEyLDEyLDAsMCwxLDAsMjRIMjEwLjE3bC0yLjcxLDE2LjIzQTQ1LjM5LDQ1LjM5LDAsMCwxLDIxMiwxNDAsNDAsNDAsMCwwLDEsMjUyLDE4MFpNMTQ0LDQ0YTEyLDEyLDAsMCwwLTEyLDEydjQ4SDUyVjU2YTEyLDEyLDAsMCwwLTI0LDBWMTc2YTEyLDEyLDAsMCwwLDI0LDBWMTI4aDgwdjQ4YTEyLDEyLDAsMCwwLDI0LDBWNTZBMTIsMTIsMCwwLDAsMTQ0LDQ0WiIvPjwvc3ZnPg==)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMDgsMzJINDhBMTYsMTYsMCwwLDAsMzIsNDhWMjA4YTE2LDE2LDAsMCwwLDE2LDE2SDIwOGExNiwxNiwwLDAsMCwxNi0xNlY0OEExNiwxNiwwLDAsMCwyMDgsMzJaTTEyOCwxNjBhOCw4LDAsMCwxLTE2LDBWMTI4SDcydjMyYTgsOCwwLDAsMS0xNiwwVjgwYTgsOCwwLDAsMSwxNiwwdjMyaDQwVjgwYTgsOCwwLDAsMSwxNiwwWm00MC00MGEzMiwzMiwwLDEsMS0yMS4zNCw1NS44NSw4LDgsMCwwLDEsMTAuNjctMTEuOTIsMTYsMTYsMCwxLDAsMC0yNCw4LDgsMCwwLDEtMTMuMTctNy42MWw4LTM4QTgsOCwwLDAsMSwxNjAsODhoMzJhOCw4LDAsMCwxLDAsMTZIMTY2LjQ5TDE2MywxMjAuMzdBMzQuMDgsMzQuMDgsMCwwLDEsMTY4LDEyMFoiLz48L3N2Zz4=)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDAsNzJWMTkyYTE2LDE2LDAsMCwxLTE2LDE2SDU2YTE2LDE2LDAsMCwxLTE2LTE2VjU2SDIyNEExNiwxNiwwLDAsMSwyNDAsNzJaIiBvcGFjaXR5PSIwLjIiLz48cGF0aCBkPSJNMTUyLDU2VjE3NmE4LDgsMCwwLDEtMTYsMFYxMjRINDh2NTJhOCw4LDAsMCwxLTE2LDBWNTZhOCw4LDAsMCwxLDE2LDB2NTJoODhWNTZhOCw4LDAsMCwxLDE2LDBabTYwLDg4YTM4LjgsMzguOCwwLDAsMC05LjQxLDEuMTRMMjA2Ljc4LDEyMEgyNDBhOCw4LDAsMCwwLDAtMTZIMjAwYTgsOCwwLDAsMC03Ljg5LDYuNjhsLTgsNDhhOCw4LDAsMCwwLDEzLjYsNi45MkExOS43MywxOS43MywwLDAsMSwyMTIsMTYwYTIwLDIwLDAsMCwxLDAsNDAsMTkuNzMsMTkuNzMsMCwwLDEtMTQuMjktNS42LDgsOCwwLDEsMC0xMS40MiwxMS4yQTM1LjU0LDM1LjU0LDAsMCwwLDIxMiwyMTZhMzYsMzYsMCwwLDAsMC03MloiLz48L3N2Zz4=)
 */
declare const I: Icon;
/** @deprecated Use TextHFiveIcon */
export declare const TextHFive: Icon;
export { I as TextHFiveIcon };
