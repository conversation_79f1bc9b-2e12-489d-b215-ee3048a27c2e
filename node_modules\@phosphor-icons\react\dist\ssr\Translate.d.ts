import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDcuMTUsMjEyLjQybC01Ni0xMTJhOCw4LDAsMCwwLTE0LjMxLDBsLTIxLjcxLDQzLjQzQTg4LDg4LDAsMCwxLDEwOCwxMjYuOTMsMTAzLjY1LDEwMy42NSwwLDAsMCwxMzUuNjksNjRIMTYwYTgsOCwwLDAsMCwwLTE2SDEwNFYzMmE4LDgsMCwwLDAtMTYsMFY0OEgzMmE4LDgsMCwwLDAsMCwxNmg4Ny42M0E4Ny43Niw4Ny43NiwwLDAsMSw5NiwxMTYuMzVhODcuNzQsODcuNzQsMCwwLDEtMTktMzEsOCw4LDAsMSwwLTE1LjA4LDUuMzRBMTAzLjYzLDEwMy42MywwLDAsMCw4NCwxMjdhODcuNTUsODcuNTUsMCwwLDEtNTIsMTcsOCw4LDAsMCwwLDAsMTYsMTAzLjQ2LDEwMy40NiwwLDAsMCw2NC0yMi4wOCwxMDQuMTgsMTA0LjE4LDAsMCwwLDUxLjQ0LDIxLjMxbC0yNi42LDUzLjE5YTgsOCwwLDAsMCwxNC4zMSw3LjE2TDE0OC45NCwxOTJoNzAuMTFsMTMuNzksMjcuNThBOCw4LDAsMCwwLDI0MCwyMjRhOCw4LDAsMCwwLDcuMTUtMTEuNThaTTE1Ni45NCwxNzYsMTg0LDEyMS44OSwyMTEuMDUsMTc2WiIvPjwvc3ZnPg==)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDMuNTgsMjE0LjIxbC01Ni0xMTJhNCw0LDAsMCwwLTcuMTYsMEwxNTcuNTUsMTQ4QTkyLjA1LDkyLjA1LDAsMCwxLDEwMiwxMjcuMzYsOTkuNjgsOTkuNjgsMCwwLDAsMTMxLjkxLDYwSDE2MGE0LDQsMCwwLDAsMC04SDEwMFYzMmE0LDQsMCwwLDAtOCwwVjUySDMyYTQsNCwwLDAsMCwwLDhoOTEuOTFBOTEuOCw5MS44LDAsMCwxLDk2LDEyMi4wNSw5Miw5MiwwLDAsMSw3My4yMyw4Ni42N2E0LDQsMCwxLDAtNy41NCwyLjY2LDk5LjU5LDk5LjU5LDAsMCwwLDI0LjMsMzhBOTEuNTksOTEuNTksMCwwLDEsMzIsMTQ4YTQsNCwwLDAsMCwwLDgsOTkuNTQsOTkuNTQsMCwwLDAsNjQtMjMuMjEsMTAwLjA5LDEwMC4wOSwwLDAsMCw1Ny42NiwyM2wtMjkuMjIsNTguNDNhNCw0LDAsMSwwLDcuMTYsMy41OEwxNDYuNDcsMTg4aDc1LjA2bDE0Ljg5LDI5Ljc5QTQsNCwwLDAsMCwyNDAsMjIwYTQuMTIsNC4xMiwwLDAsMCwxLjc5LS40MkE0LDQsMCwwLDAsMjQzLjU4LDIxNC4yMVpNMTUwLjQ3LDE4MCwxODQsMTEyLjk0LDIxNy41MywxODBaIi8+PC9zdmc+)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDUuMzcsMjEzLjMybC01Ni0xMTJhNiw2LDAsMCwwLTEwLjc0LDBsLTIyLjMsNDQuNkE5MCw5MCwwLDAsMSwxMDUsMTI3LjE5LDEwMS43MywxMDEuNzMsMCwwLDAsMTMzLjgyLDYySDE2MGE2LDYsMCwwLDAsMC0xMkgxMDJWMzJhNiw2LDAsMCwwLTEyLDBWNTBIMzJhNiw2LDAsMCwwLDAsMTJoODkuNzlBODkuNzEsODkuNzEsMCwwLDEsOTYsMTE5LjIzLDg5LjgxLDg5LjgxLDAsMCwxLDc1LjExLDg2LDYsNiwwLDEsMCw2My44LDkwLDEwMS42NiwxMDEuNjYsMCwwLDAsODcsMTI3LjIsODkuNTYsODkuNTYsMCwwLDEsMzIsMTQ2YTYsNiwwLDAsMCwwLDEyLDEwMS41NSwxMDEuNTUsMCwwLDAsNjQtMjIuNjMsMTAyLjExLDEwMi4xMSwwLDAsMCw1NC41MywyMi4xN2wtMjcuODksNTUuNzhhNiw2LDAsMCwwLDEwLjc0LDUuMzZMMTQ3LjcxLDE5MGg3Mi41OGwxNC4zNCwyOC42OEE2LDYsMCwwLDAsMjQwLDIyMmE1Ljg3LDUuODcsMCwwLDAsMi42OC0uNjRBNiw2LDAsMCwwLDI0NS4zNywyMTMuMzJaTTE1My43MSwxNzgsMTg0LDExNy40MiwyMTQuMjksMTc4WiIvPjwvc3ZnPg==)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNTAuNzMsMjEwLjYzbC01Ni0xMTJhMTIsMTIsMCwwLDAtMjEuNDYsMGwtMjAuNTIsNDFBODQuMiw4NC4yLDAsMCwxLDExNCwxMjYuMjIsMTA3LjQ4LDEwNy40OCwwLDAsMCwxMzkuMzMsNjhIMTYwYTEyLDEyLDAsMCwwLDAtMjRIMTA4VjMyYTEyLDEyLDAsMCwwLTI0LDBWNDRIMzJhMTIsMTIsMCwwLDAsMCwyNGg4My4xM0E4My42OSw4My42OSwwLDAsMSw5NiwxMTAuMzUsODQsODQsMCwwLDEsODMuNiw5MWExMiwxMiwwLDEsMC0yMS44MSwxMEExMDcuNTUsMTA3LjU1LDAsMCwwLDc4LDEyNi4yNCw4My41NCw4My41NCwwLDAsMSwzMiwxNDBhMTIsMTIsMCwwLDAsMCwyNCwxMDcuNDcsMTA3LjQ3LDAsMCwwLDY0LTIxLjA3LDEwOC40LDEwOC40LDAsMCwwLDQ1LjM5LDE5LjQ0bC0yNC4xMyw0OC4yNmExMiwxMiwwLDEsMCwyMS40NiwxMC43M0wxNTEuNDEsMTk2aDY1LjE3bDEyLjY4LDI1LjM2YTEyLDEyLDAsMSwwLDIxLjQ3LTEwLjczWk0xNjMuNDEsMTcyLDE4NCwxMzAuODMsMjA0LjU4LDE3MloiLz48L3N2Zz4=)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjAsMTI5Ljg5LDE3NS4wNiwxNjBIMTQ0Ljk0bDYuMzYtMTIuN3YwWk0yMjQsNDhWMjA4YTE2LDE2LDAsMCwxLTE2LDE2SDQ4YTE2LDE2LDAsMCwxLTE2LTE2VjQ4QTE2LDE2LDAsMCwxLDQ4LDMySDIwOEExNiwxNiwwLDAsMSwyMjQsNDhaTTIwNy4xNiwxODguNDJsLTQwLTgwYTgsOCwwLDAsMC0xNC4zMiwwTDEzOS42NiwxMzQuOGE2Mi4zMSw2Mi4zMSwwLDAsMS0yMy42MS0xMEE3OS42MSw3OS42MSwwLDAsMCwxMzUuNiw4MEgxNTJhOCw4LDAsMCwwLDAtMTZIMTEyVjU2YTgsOCwwLDAsMC0xNiwwdjhINTZhOCw4LDAsMCwwLDAsMTZoNjMuNDhhNjMuNzMsNjMuNzMsMCwwLDEtMTUuMywzNC4wNSw2NS45Myw2NS45MywwLDAsMS05LTEzLjYxLDgsOCwwLDAsMC0xNC4zMiw3LjEyLDgxLjc1LDgxLjc1LDAsMCwwLDExLjQsMTcuMTVBNjMuNjIsNjMuNjIsMCwwLDEsNTYsMTM2YTgsOCwwLDAsMCwwLDE2LDc5LjU2LDc5LjU2LDAsMCwwLDQ4LjExLTE2LjEzLDc4LjMzLDc4LjMzLDAsMCwwLDI4LjE4LDEzLjY2bC0xOS40NSwzOC44OWE4LDgsMCwwLDAsMTQuMzIsNy4xNkwxMzYuOTQsMTc2aDQ2LjEybDkuNzgsMTkuNThhOCw4LDAsMSwwLDE0LjMyLTcuMTZaIi8+PC9zdmc+)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMjQsMTg0SDE0NGw0MC04MFpNOTYsMTI3LjU2aDBBOTUuNzgsOTUuNzgsMCwwLDAsMTI4LDU2SDY0QTk1Ljc4LDk1Ljc4LDAsMCwwLDk2LDEyNy41NloiIG9wYWNpdHk9IjAuMiIvPjxwYXRoIGQ9Ik0yNDcuMTUsMjEyLjQybC01Ni0xMTJhOCw4LDAsMCwwLTE0LjMxLDBsLTIxLjcxLDQzLjQzQTg4LDg4LDAsMCwxLDEwOCwxMjYuOTMsMTAzLjY1LDEwMy42NSwwLDAsMCwxMzUuNjksNjRIMTYwYTgsOCwwLDAsMCwwLTE2SDEwNFYzMmE4LDgsMCwwLDAtMTYsMFY0OEgzMmE4LDgsMCwwLDAsMCwxNmg4Ny42M0E4Ny43LDg3LjcsMCwwLDEsOTYsMTE2LjM1YTg3Ljc0LDg3Ljc0LDAsMCwxLTE5LTMxLDgsOCwwLDEsMC0xNS4wOCw1LjM0QTEwMy42MywxMDMuNjMsMCwwLDAsODQsMTI3YTg3LjU1LDg3LjU1LDAsMCwxLTUyLDE3LDgsOCwwLDAsMCwwLDE2LDEwMy40NiwxMDMuNDYsMCwwLDAsNjQtMjIuMDgsMTA0LjE4LDEwNC4xOCwwLDAsMCw1MS40NCwyMS4zMWwtMjYuNiw1My4xOWE4LDgsMCwwLDAsMTQuMzEsNy4xNkwxNDguOTQsMTkyaDcwLjExbDEzLjc5LDI3LjU4QTgsOCwwLDAsMCwyNDAsMjI0YTgsOCwwLDAsMCw3LjE1LTExLjU4Wk0xNTYuOTQsMTc2LDE4NCwxMjEuODksMjExLjA1LDE3NloiLz48L3N2Zz4=)
 */
declare const I: Icon;
/** @deprecated Use TranslateIcon */
export declare const Translate: Icon;
export { I as TranslateIcon };
