module.exports = {
  allowCache: true,
  dateRanges: [],
  iFramePlaylists: [],
  mediaSequence: 0,
  playlistType: 'VOD',
  segments: [
    {
      byterange: {
        length: 522828,
        offset: 0
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 587500,
        offset: 522828
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts',
      title: ';asljasdfii11)))00,'
    },
    {
      byterange: {
        length: 713084,
        offset: 1110328
      },
      duration: 5,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 476580,
        offset: 1823412
      },
      duration: 9.7,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 535612,
        offset: 2299992
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 207176,
        offset: 2835604
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 455900,
        offset: 3042780
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 657248,
        offset: 3498680
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 571708,
        offset: 4155928
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 485040,
        offset: 4727636
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 709136,
        offset: 5212676
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 730004,
        offset: 5921812
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 456276,
        offset: 6651816
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 468684,
        offset: 7108092
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 444996,
        offset: 7576776
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 331444,
        offset: 8021772
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    },
    {
      byterange: {
        length: 44556,
        offset: 8353216
      },
      duration: 10,
      timeline: 0,
      uri: 'hls_450k_video.ts'
    }
  ],
  targetDuration: 10,
  endList: true,
  discontinuitySequence: 0,
  discontinuityStarts: [],
  version: 3
};
