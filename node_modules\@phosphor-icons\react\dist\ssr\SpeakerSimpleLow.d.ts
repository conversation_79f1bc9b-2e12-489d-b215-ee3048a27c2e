import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjMuNTEsMjQuODFhOCw4LDAsMCwwLTguNDIuODhMODUuMjUsODBINDBBMTYsMTYsMCwwLDAsMjQsOTZ2NjRhMTYsMTYsMCwwLDAsMTYsMTZIODUuMjVsNjkuODQsNTQuMzFBOCw4LDAsMCwwLDE2OCwyMjRWMzJBOCw4LDAsMCwwLDE2My41MSwyNC44MVpNMTUyLDIwNy42NCw5Mi45MSwxNjEuNjlBNy45NCw3Ljk0LDAsMCwwLDg4LDE2MEg0MFY5Nkg4OGE3Ljk0LDcuOTQsMCwwLDAsNC45MS0xLjY5TDE1Miw0OC4zNlpNMjA4LDEwNHY0OGE4LDgsMCwwLDEtMTYsMFYxMDRhOCw4LDAsMCwxLDE2LDBaIi8+PC9zdmc+)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjEuNzYsMjguNDFhNCw0LDAsMCwwLTQuMjIuNDNMODYuNjMsODRINDBBMTIsMTIsMCwwLDAsMjgsOTZ2NjRhMTIsMTIsMCwwLDAsMTIsMTJIODYuNjNsNzAuOTEsNTUuMTZBNC4wNyw0LjA3LDAsMCwwLDE2MCwyMjhhMy45MiwzLjkyLDAsMCwwLDEuNzYtLjQxQTQsNCwwLDAsMCwxNjQsMjI0VjMyQTQsNCwwLDAsMCwxNjEuNzYsMjguNDFaTTE1NiwyMTUuODJsLTY1LjU0LTUxQTQuMDYsNC4wNiwwLDAsMCw4OCwxNjRINDBhNCw0LDAsMCwxLTQtNFY5NmE0LDQsMCwwLDEsNC00SDg4YTQuMDYsNC4wNiwwLDAsMCwyLjQ2LS44NGw2NS41NC01MVpNMjA0LDEwNHY0OGE0LDQsMCwwLDEtOCwwVjEwNGE0LDQsMCwwLDEsOCwwWiIvPjwvc3ZnPg==)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjIuNjQsMjYuNjFhNiw2LDAsMCwwLTYuMzIuNjVMODUuOTQsODJINDBBMTQsMTQsMCwwLDAsMjYsOTZ2NjRhMTQsMTQsMCwwLDAsMTQsMTRIODUuOTRsNzAuMzgsNTQuNzRBNiw2LDAsMCwwLDE2NiwyMjRWMzJBNiw2LDAsMCwwLDE2Mi42NCwyNi42MVpNMTU0LDIxMS43Myw5MS42OCwxNjMuMjZBNiw2LDAsMCwwLDg4LDE2Mkg0MGEyLDIsMCwwLDEtMi0yVjk2YTIsMiwwLDAsMSwyLTJIODhhNiw2LDAsMCwwLDMuNjgtMS4yNkwxNTQsNDQuMjdaTTIwNiwxMDR2NDhhNiw2LDAsMCwxLTEyLDBWMTA0YTYsNiwwLDAsMSwxMiwwWiIvPjwvc3ZnPg==)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjUuMjcsMjEuMjJhMTIsMTIsMCwwLDAtMTIuNjQsMS4zMUw4My44OCw3Nkg0MEEyMCwyMCwwLDAsMCwyMCw5NnY2NGEyMCwyMCwwLDAsMCwyMCwyMEg4My44OGw2OC43NSw1My40N0ExMiwxMiwwLDAsMCwxNzIsMjI0VjMyQTEyLDEyLDAsMCwwLDE2NS4yNywyMS4yMlpNMTQ4LDE5OS40Niw5NS4zNywxNTguNTNBMTIsMTIsMCwwLDAsODgsMTU2SDQ0VjEwMEg4OGExMiwxMiwwLDAsMCw3LjM3LTIuNTNMMTQ4LDU2LjU0Wk0yMTIsMTA0djQ4YTEyLDEyLDAsMCwxLTI0LDBWMTA0YTEyLDEyLDAsMCwxLDI0LDBaIi8+PC9zdmc+)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjgsMzJWMjI0YTgsOCwwLDAsMS0xMi45MSw2LjMxTDg1LjI1LDE3Nkg0MGExNiwxNiwwLDAsMS0xNi0xNlY5NkExNiwxNiwwLDAsMSw0MCw4MEg4NS4yNWw2OS44NC01NC4zMUE4LDgsMCwwLDEsMTY4LDMyWm0zMiw2NGE4LDgsMCwwLDAtOCw4djQ4YTgsOCwwLDAsMCwxNiwwVjEwNEE4LDgsMCwwLDAsMjAwLDk2WiIvPjwvc3ZnPg==)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNjAsMzJWMjI0TDg4LDE2OEg0MGE4LDgsMCwwLDEtOC04Vjk2YTgsOCwwLDAsMSw4LThIODhaIiBvcGFjaXR5PSIwLjIiLz48cGF0aCBkPSJNMTYzLjUxLDI0LjgxYTgsOCwwLDAsMC04LjQyLjg4TDg1LjI1LDgwSDQwQTE2LDE2LDAsMCwwLDI0LDk2djY0YTE2LDE2LDAsMCwwLDE2LDE2SDg1LjI1bDY5Ljg0LDU0LjMxQTgsOCwwLDAsMCwxNjgsMjI0VjMyQTgsOCwwLDAsMCwxNjMuNTEsMjQuODFaTTE1MiwyMDcuNjQsOTIuOTEsMTYxLjY5QTcuOTQsNy45NCwwLDAsMCw4OCwxNjBINDBWOTZIODhhNy45NCw3Ljk0LDAsMCwwLDQuOTEtMS42OUwxNTIsNDguMzZaTTIwOCwxMDR2NDhhOCw4LDAsMCwxLTE2LDBWMTA0YTgsOCwwLDAsMSwxNiwwWiIvPjwvc3ZnPg==)
 */
declare const I: Icon;
/** @deprecated Use SpeakerSimpleLowIcon */
export declare const SpeakerSimpleLow: Icon;
export { I as SpeakerSimpleLowIcon };
