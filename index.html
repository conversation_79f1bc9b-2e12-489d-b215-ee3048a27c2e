
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ZimFlix - Zimbabwe's Premier Streaming Platform</title>
    <meta name="description" content="ZimFlix - Stream the best of Zimbabwean and international entertainment. Movies, TV shows, documentaries and more." />
    <meta name="author" content="ZimFlix Zimbabwe" />

    <meta property="og:title" content="ZimFlix - Zimbabwe's Premier Streaming Platform" />
    <meta property="og:description" content="Stream the best of Zimbabwean and international entertainment" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@zimflix" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  </head>

  <body>
    <div id="root"></div>
    <script>
      // Defensive code for browser extension compatibility
      window.addEventListener('error', function(e) {
        // Suppress errors from browser extensions
        if (e.filename && e.filename.includes('chrome-extension://')) {
          e.preventDefault();
          return true;
        }
      });
      
      // Add empty objects that some extensions might expect
      if (typeof window.chrome === 'undefined') {
        window.chrome = {};
      }
      
      // Prevent extension injection errors by providing fallback objects
      window.addEventListener('DOMContentLoaded', function() {
        // Create any missing global objects that extensions might expect
        if (!window.extensionConfig) {
          window.extensionConfig = { enable: false, id: null };
        }
      });
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
