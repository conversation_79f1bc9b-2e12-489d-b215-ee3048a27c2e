{"version": 3, "file": "react-router-dom.production.min.js", "sources": ["../../dom.ts", "../../index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "_formDataSupportsSubmitter", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "has", "getFormSubmissionInfo", "target", "basename", "method", "action", "formData", "body", "toLowerCase", "attr", "getAttribute", "stripBasename", "FormData", "isButtonElement", "isInputElement", "type", "form", "Error", "document", "createElement", "e", "isFormDataSubmitterSupported", "name", "prefix", "append", "undefined", "window", "__reactRouterVersion", "parseHydrationData", "_window", "state", "__staticRouterHydrationData", "errors", "_extends", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "FetchersContext", "Map", "startTransitionImpl", "flushSyncImpl", "ReactDOM", "useIdImpl", "flushSyncSafe", "cb", "Deferred", "constructor", "this", "promise", "Promise", "resolve", "reject", "reason", "MemoizedDataRoutes", "DataRoutes", "_ref3", "routes", "future", "useRoutesImpl", "UNSAFE_useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "_ref7", "ref", "absoluteHref", "onClick", "relative", "reloadDocument", "replace", "to", "preventScrollReset", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "isExternal", "test", "currentUrl", "URL", "location", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "event", "defaultPrevented", "NavLink", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "children", "_excluded2", "useResolvedPath", "useLocation", "routerState", "DataRouterStateContext", "UNSAFE_DataRouterStateContext", "navigator", "useViewTransitionState", "toPathname", "encodeLocation", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "navigate", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "DataRouterHook", "DataRouterStateHook", "useDataRouterContext", "<PERSON><PERSON><PERSON>", "ctx", "DataRouterContext", "UNSAFE_DataRouterContext", "invariant", "UNSAFE_invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "useCallback", "button", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isModifiedEvent", "shouldProcessLinkClick", "createPath", "fetcherId", "getUniqueFetcherId", "String", "router", "UseSubmit", "currentRouteId", "useRouteId", "options", "validateClientSideSubmission", "fetch", "formEncType", "flushSync", "fromRouteId", "_temp2", "routeContext", "RouteContext", "UNSAFE_RouteContext", "match", "matches", "slice", "params", "indexValues", "getAll", "some", "delete", "for<PERSON>ach", "qs", "toString", "route", "index", "joinPaths", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "useScrollRestoration", "_temp4", "<PERSON><PERSON><PERSON>", "storageKey", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "useEffect", "history", "scrollRestoration", "callback", "capture", "opts", "addEventListener", "removeEventListener", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "useLayoutEffect", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "scrollTo", "vtContext", "currentPath", "currentLocation", "nextPath", "nextLocation", "matchPath", "_ref4", "historyRef", "useRef", "current", "createBrowserHistory", "v5Compat", "setStateImpl", "useState", "v7_startTransition", "setState", "newState", "listen", "Router", "navigationType", "_ref5", "createHashHistory", "_ref", "fallbackElement", "pendingState", "setPendingState", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "optInStartTransition", "startTransitionSafe", "_ref2", "deletedFetchers", "viewTransitionOpts", "fetchers", "fetcher", "set", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "t", "finished", "finally", "subscribe", "renderPromise", "async", "useMemo", "createHref", "go", "n", "push", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "Fragment", "Provider", "historyAction", "initialized", "v7_partialHydration", "_ref10", "createRouter", "v7_prependBasename", "hydrationData", "mapRouteProperties", "UNSAFE_mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "_ref6", "_ref12", "when", "blocker", "useBlocker", "confirm", "setTimeout", "proceed", "reset", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "UseFetchers", "from", "_ref11", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "searchParams", "locationSearch", "defaultSearchParams", "_", "getSearchParamsForLocation", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams"], "mappings": ";;;;;;;;;;0lCAOO,MAAMA,EAAgC,MACvCC,EAA8B,oCAE7B,SAASC,EAAcC,GAC5B,OAAiB,MAAVA,GAA4C,iBAAnBA,EAAOC,OACzC,CA+DO,SAASC,EACdC,GAEA,YAFyB,IAAzBA,IAAAA,EAA4B,IAErB,IAAIC,gBACO,iBAATD,GACPE,MAAMC,QAAQH,IACdA,aAAgBC,gBACZD,EACAI,OAAOC,KAAKL,GAAMM,QAAO,CAACC,EAAMC,KAC9B,IAAIC,EAAQT,EAAKQ,GACjB,OAAOD,EAAKG,OACVR,MAAMC,QAAQM,GAASA,EAAME,KAAKC,GAAM,CAACJ,EAAKI,KAAM,CAAC,CAACJ,EAAKC,IAC5D,GACA,IAEX,CA4CA,IAAII,EAA6C,KAgGjD,MAAMC,EAA0C,IAAIC,IAAI,CACtD,oCACA,sBACA,eAGF,SAASC,EAAeC,GACtB,OAAe,MAAXA,GAAoBH,EAAsBI,IAAID,GAS3CA,EAFE,IAGX,CAEO,SAASE,EACdC,EACAC,GAQA,IAAIC,EACAC,EACAN,EACAO,EACAC,EAEJ,GAtPO7B,EADqBC,EAuPVuB,IAtP+C,SAAjCvB,EAAOC,QAAQ4B,cAsPpB,CAIzB,IAAIC,EAAOP,EAAOQ,aAAa,UAC/BL,EAASI,EAAOE,EAAaA,cAACF,EAAMN,GAAY,KAChDC,EAASF,EAAOQ,aAAa,WAAalC,EAC1CuB,EAAUD,EAAeI,EAAOQ,aAAa,aAAejC,EAE5D6B,EAAW,IAAIM,SAASV,EACzB,MAAM,GArQF,SAAyBvB,GAC9B,OAAOD,EAAcC,IAA4C,WAAjCA,EAAOC,QAAQ4B,aACjD,CAoQIK,CAAgBX,IA9Pb,SAAwBvB,GAC7B,OAAOD,EAAcC,IAA4C,UAAjCA,EAAOC,QAAQ4B,aACjD,CA6PKM,CAAeZ,KACG,WAAhBA,EAAOa,MAAqC,UAAhBb,EAAOa,MACtC,CACA,IAAIC,EAAOd,EAAOc,KAElB,GAAY,MAARA,EACF,MAAM,IAAIC,MAAK,sEAUjB,IAAIR,EAAOP,EAAOQ,aAAa,eAAiBM,EAAKN,aAAa,UAmBlE,GAlBAL,EAASI,EAAOE,EAAaA,cAACF,EAAMN,GAAY,KAEhDC,EACEF,EAAOQ,aAAa,eACpBM,EAAKN,aAAa,WAClBlC,EACFuB,EACED,EAAeI,EAAOQ,aAAa,iBACnCZ,EAAekB,EAAKN,aAAa,aACjCjC,EAGF6B,EAAW,IAAIM,SAASI,EAAMd,IA1KlC,WACE,GAAmC,OAA/BP,EACF,IACE,IAAIiB,SACFM,SAASC,cAAc,QAEvB,GAEFxB,GAA6B,CAG/B,CAFE,MAAOyB,GACPzB,GAA6B,CAC/B,CAEF,OAAOA,CACT,CAkKS0B,GAAgC,CACnC,IAAIC,KAAEA,EAAIP,KAAEA,EAAIxB,MAAEA,GAAUW,EAC5B,GAAa,UAATa,EAAkB,CACpB,IAAIQ,EAASD,EAAUA,MAAU,GACjChB,EAASkB,OAAUD,EAAM,IAAK,KAC9BjB,EAASkB,OAAUD,EAAM,IAAK,IAC/B,MAAUD,GACThB,EAASkB,OAAOF,EAAM/B,EAE1B,CACF,KAAO,IAAIb,EAAcwB,GACvB,MAAM,IAAIe,MACR,sFAIFb,EAAS5B,EACT6B,EAAS,KACTN,EAAUtB,EACV8B,EAAOL,CACT,CA1TK,IAAuBvB,EAkU5B,OALI2B,GAAwB,eAAZP,IACdQ,EAAOD,EACPA,OAAWmB,GAGN,CAAEpB,SAAQD,OAAQA,EAAOI,cAAeT,UAASO,WAAUC,OACpE,kWC9FA,IACEmB,OAAOC,qBAHT,GAKE,CADA,MAAOP,GACP,CAwDF,SAASQ,IAAiD,IAAAC,EACxD,IAAIC,EAAQD,OAAHA,EAAGH,aAAAG,EAAAA,EAAQE,4BAOpB,OANID,GAASA,EAAME,SACjBF,EAAKG,EAAA,CAAA,EACAH,EAAK,CACRE,OAAQE,EAAkBJ,EAAME,WAG7BF,CACT,CAEA,SAASI,EACPF,GAEA,IAAKA,EAAQ,OAAO,KACpB,IAAIG,EAAUjD,OAAOiD,QAAQH,GACzBI,EAA6C,CAAA,EACjD,IAAK,IAAK9C,EAAK+C,KAAQF,EAGrB,GAAIE,GAAsB,uBAAfA,EAAIC,OACbF,EAAW9C,GAAO,IAAIiD,EAAAA,yBACpBF,EAAIG,OACJH,EAAII,WACJJ,EAAIK,MACa,IAAjBL,EAAIM,eAED,GAAIN,GAAsB,UAAfA,EAAIC,OAAoB,CAExC,GAAID,EAAIO,UAAW,CACjB,IAAIC,EAAmBnB,OAAOW,EAAIO,WAClC,GAAgC,mBAArBC,EACT,IAEE,IAAIC,EAAQ,IAAID,EAAiBR,EAAIU,SAGrCD,EAAME,MAAQ,GACdZ,EAAW9C,GAAOwD,CAElB,CADA,MAAO1B,GACP,CAGN,CAEA,GAAuB,MAAnBgB,EAAW9C,GAAc,CAC3B,IAAIwD,EAAQ,IAAI7B,MAAMoB,EAAIU,SAG1BD,EAAME,MAAQ,GACdZ,EAAW9C,GAAOwD,CACpB,CACF,MACEV,EAAW9C,GAAO+C,EAGtB,OAAOD,CACT,CAmBA,MAAMa,EAAwBC,EAAMC,cAA2C,CAC7EC,iBAAiB,IAWbC,EAAkBH,EAAMC,cAAqC,IAAIG,KAmCjEC,EAAsBL,EAAsB,gBAE5CM,EAAgBC,EAAmB,UAEnCC,EAAYR,EAAY,MAU9B,SAASS,EAAcC,GACjBJ,EACFA,EAAcI,GAEdA,GAEJ,CASA,MAAMC,EAOJC,cAAcC,KANdvB,OAA8C,UAO5CuB,KAAKC,QAAU,IAAIC,SAAQ,CAACC,EAASC,KACnCJ,KAAKG,QAAW3E,IACM,YAAhBwE,KAAKvB,SACPuB,KAAKvB,OAAS,WACd0B,EAAQ3E,GACV,EAEFwE,KAAKI,OAAUC,IACO,YAAhBL,KAAKvB,SACPuB,KAAKvB,OAAS,WACd2B,EAAOC,GACT,CACD,GAEL,EAqRF,MAAMC,EAAqBnB,EAAM7D,KAAKiF,GAEtC,SAASA,EAAUC,GAQW,IARVC,OAClBA,EAAMC,OACNA,EAAM3C,MACNA,GAKDyC,EACC,OAAOG,EAAaC,qBAACH,OAAQ/C,EAAWK,EAAO2C,EACjD,CAuKA,MAAMG,EACc,oBAAXlD,aACoB,IAApBA,OAAOR,eAC2B,IAAlCQ,OAAOR,SAASC,cAEnB0D,EAAqB,gCAKdC,EAAO5B,EAAM6B,YACxB,SAAoBC,EAalBC,GACA,IAIIC,GAjBJC,QACEA,EAAOC,SACPA,EAAQC,eACRA,EAAcC,QACdA,EAAOxD,MACPA,EAAK5B,OACLA,EAAMqF,GACNA,EAAEC,mBACFA,EAAkBC,eAClBA,GAEDT,EADIU,EAAIC,EAAAX,EAAAY,IAILzF,SAAEA,GAAa+C,EAAM2C,WAAWC,EAAAA,0BAIhCC,GAAa,EAEjB,GAAkB,iBAAPR,GAAmBV,EAAmBmB,KAAKT,KAEpDL,EAAeK,EAGXX,GACF,IACE,IAAIqB,EAAa,IAAIC,IAAIxE,OAAOyE,SAASC,MACrCC,EAAYd,EAAGe,WAAW,MAC1B,IAAIJ,IAAID,EAAWM,SAAWhB,GAC9B,IAAIW,IAAIX,GACRiB,EAAO7F,EAAaA,cAAC0F,EAAUI,SAAUtG,GAEzCkG,EAAUK,SAAWT,EAAWS,QAAkB,MAARF,EAE5CjB,EAAKiB,EAAOH,EAAUM,OAASN,EAAUO,KAEzCb,GAAa,CASjB,CAPE,MAAO3E,GAOT,CAKJ,IAAIgF,EAAOS,EAAOA,QAACtB,EAAI,CAAEH,aAErB0B,EAAkBC,EAAoBxB,EAAI,CAC5CD,UACAxD,QACA5B,SACAsF,qBACAJ,WACAK,mBAWF,OAEEvC,EAAA/B,cAAAc,IAAAA,KACMyD,EAAI,CACRU,KAAMlB,GAAgBkB,EACtBjB,QAASY,GAAcV,EAAiBF,EAd5C,SACE6B,GAEI7B,GAASA,EAAQ6B,GAChBA,EAAMC,kBACTH,EAAgBE,EAEpB,EAQI/B,IAAKA,EACL/E,OAAQA,IAGd,IA2BWgH,EAAUhE,EAAM6B,YAC3B,SAAuBoC,EAYrBlC,GACA,IAXE,eAAgBmC,EAAkB,OAAMC,cACxCA,GAAgB,EAChBC,UAAWC,EAAgB,GAAEC,IAC7BA,GAAM,EACNC,MAAOC,EAASnC,GAChBA,EAAEE,eACFA,EAAckC,SACdA,GAEDR,EADIzB,EAAIC,EAAAwB,EAAAS,GAILpB,EAAOqB,EAAeA,gBAACtC,EAAI,CAAEH,SAAUM,EAAKN,WAC5Ce,EAAW2B,EAAAA,cACXC,EAAc7E,EAAM2C,WAAWmC,EAAsBC,gCACrDC,UAAEA,EAAS/H,SAAEA,GAAa+C,EAAM2C,WAAWC,EAAAA,0BAC3C1C,EACa,MAAf2E,GAGAI,EAAuB3B,KACJ,IAAnBf,EAEE2C,EAAaF,EAAUG,eACvBH,EAAUG,eAAe7B,GAAMC,SAC/BD,EAAKC,SACL6B,EAAmBnC,EAASM,SAC5B8B,EACFR,GAAeA,EAAYS,YAAcT,EAAYS,WAAWrC,SAC5D4B,EAAYS,WAAWrC,SAASM,SAChC,KAEDY,IACHiB,EAAmBA,EAAiB9H,cACpC+H,EAAuBA,EACnBA,EAAqB/H,cACrB,KACJ4H,EAAaA,EAAW5H,eAGtB+H,GAAwBpI,IAC1BoI,EACE5H,EAAAA,cAAc4H,EAAsBpI,IAAaoI,GAQrD,MAAME,EACW,MAAfL,GAAsBA,EAAWM,SAAS,KACtCN,EAAWO,OAAS,EACpBP,EAAWO,OACjB,IAqBIrB,EArBAsB,EACFN,IAAqBF,IACnBZ,GACAc,EAAiBhC,WAAW8B,IACkB,MAA9CE,EAAiBO,OAAOJ,GAExBK,EACsB,MAAxBP,IACCA,IAAyBH,IACtBZ,GACAe,EAAqBjC,WAAW8B,IACmB,MAAnDG,EAAqBM,OAAOT,EAAWO,SAEzCI,EAAc,CAChBH,WACAE,YACA1F,mBAGE4F,EAAcJ,EAAWxB,OAAkB3F,EAI7C6F,EAD2B,mBAAlBC,EACGA,EAAcwB,GAOd,CACVxB,EACAqB,EAAW,SAAW,KACtBE,EAAY,UAAY,KACxB1F,EAAkB,gBAAkB,MAEnC6F,OAAOC,SACPC,KAAK,KAGV,IAAI1B,EACmB,mBAAdC,EAA2BA,EAAUqB,GAAerB,EAE7D,OACExE,EAAA/B,cAAC2D,EAAI7C,KACCyD,EAAI,CACR,eAAcsD,EACd1B,UAAWA,EACXrC,IAAKA,EACLwC,MAAOA,EACPlC,GAAIA,EACJE,eAAgBA,IAEK,mBAAbkC,EAA0BA,EAASoB,GAAepB,EAGhE,IA2GWyB,EAAOlG,EAAM6B,YACxB,CAAAsE,EAeEC,KACG,IAfHC,WACEA,EAAUC,SACVA,EAAQnE,eACRA,EAAcC,QACdA,EAAOxD,MACPA,EAAK1B,OACLA,EAAS5B,EAAa6B,OACtBA,EAAMoJ,SACNA,EAAQrE,SACRA,EAAQI,mBACRA,EAAkBC,eAClBA,GAED4D,EADIK,EAAK/D,EAAA0D,EAAAM,GAINC,EAASC,IACTC,EAAaC,EAAc1J,EAAQ,CAAE+E,aACrC4E,EACuB,QAAzB5J,EAAOI,cAA0B,MAAQ,OA0B3C,OACE0C,EAAA/B,cAAA,OAAAc,EAAA,CACEgD,IAAKqE,EACLlJ,OAAQ4J,EACR3J,OAAQyJ,EACRL,SAAUpE,EAAiBoE,EA7B+BzC,IAE5D,GADAyC,GAAYA,EAASzC,GACjBA,EAAMC,iBAAkB,OAC5BD,EAAMiD,iBAEN,IAAIC,EAAalD,EAAqCmD,YACnDD,UAECE,GACDF,MAAAA,OAAAA,EAAAA,EAAWxJ,aAAa,gBACzBN,EAEFwJ,EAAOM,GAAalD,EAAMqD,cAAe,CACvCd,aACAnJ,OAAQgK,EACRZ,WACAlE,UACAxD,QACAsD,WACAI,qBACAC,kBACA,GASIiE,GACJ,IAiCR,IAEKY,WAAAA,GAAc,OAAdA,EAAc,qBAAA,uBAAdA,EAAc,UAAA,YAAdA,EAAc,iBAAA,mBAAdA,EAAc,WAAA,aAAdA,EAAc,uBAAA,yBAAdA,CAAc,EAAdA,GAAc,CAAA,GAQdC,WAAAA,GAAmB,OAAnBA,EAAmB,WAAA,aAAnBA,EAAmB,YAAA,cAAnBA,EAAmB,qBAAA,uBAAnBA,CAAmB,EAAnBA,GAML,CAAA,GAQA,SAASC,EAAqBC,GAC5B,IAAIC,EAAMxH,EAAM2C,WAAW8E,EAAiBC,0BAE5C,OADUF,GAAVG,EAASC,kBAAA,GACFJ,CACT,CAEA,SAASK,EAAmBN,GAC1B,IAAI3I,EAAQoB,EAAM2C,WAAWmC,EAAsBC,+BAEnD,OADUnG,GAAV+I,EAASC,kBAAA,GACFhJ,CACT,CASO,SAASiF,EACdxB,EAAMyF,GAgB4C,IAflD9K,OACEA,EACAoF,QAAS2F,EAAWnJ,MACpBA,EAAK0D,mBACLA,EAAkBJ,SAClBA,EAAQK,eACRA,QAQD,IAAAuF,EAAG,CAAA,EAAEA,EAEFxB,EAAW0B,EAAAA,cACX/E,EAAW2B,EAAAA,cACXtB,EAAOqB,EAAeA,gBAACtC,EAAI,CAAEH,aAEjC,OAAOlC,EAAMiI,aACVnE,IACC,GDl3CC,SACLA,EACA9G,GAEA,QACmB,IAAjB8G,EAAMoE,QACJlL,GAAqB,UAAXA,GAVhB,SAAyB8G,GACvB,SAAUA,EAAMqE,SAAWrE,EAAMsE,QAAUtE,EAAMuE,SAAWvE,EAAMwE,SACpE,CASKC,CAAgBzE,GAErB,CCy2CU0E,CAAuB1E,EAAO9G,GAAS,CACzC8G,EAAMiD,iBAIN,IAAI3E,OACc7D,IAAhBwJ,EACIA,EACAU,EAAUA,WAACxF,KAAcwF,aAAWnF,GAE1CgD,EAASjE,EAAI,CACXD,UACAxD,QACA0D,qBACAJ,WACAK,kBAEJ,IAEF,CACEU,EACAqD,EACAhD,EACAyE,EACAnJ,EACA5B,EACAqF,EACAC,EACAJ,EACAK,GAGN,CAkGA,IAAImG,EAAY,EACZC,EAAqBA,IAAA,KAAWC,SAASF,GAAc,KAMpD,SAAS/B,IACd,IAAIkC,OAAEA,GAAWvB,EAAqBF,EAAe0B,YACjD7L,SAAEA,GAAa+C,EAAM2C,WAAWC,EAAAA,0BAChCmG,EAAiBC,EAAAA,oBAErB,OAAOhJ,EAAMiI,aACX,SAACjL,EAAQiM,QAAO,IAAPA,IAAAA,EAAU,CAAA,GAtBvB,WACE,GAAwB,oBAAbjL,SACT,MAAM,IAAID,MACR,gHAIN,CAgBMmL,GAEA,IAAI/L,OAAEA,EAAMD,OAAEA,EAAML,QAAEA,EAAOO,SAAEA,EAAQC,KAAEA,GAASN,EAChDC,EACAC,GAGF,IAAyB,IAArBgM,EAAQ3C,SAAoB,CAC9B,IAAIlK,EAAM6M,EAAQ5C,YAAcsC,IAChCE,EAAOM,MAAM/M,EAAK2M,EAAgBE,EAAQ9L,QAAUA,EAAQ,CAC1DmF,mBAAoB2G,EAAQ3G,mBAC5BlF,WACAC,OACAyJ,WAAYmC,EAAQ/L,QAAWA,EAC/BkM,YAAaH,EAAQpM,SAAYA,EACjCwM,UAAWJ,EAAQI,WAEvB,MACER,EAAOvC,SAAS2C,EAAQ9L,QAAUA,EAAQ,CACxCmF,mBAAoB2G,EAAQ3G,mBAC5BlF,WACAC,OACAyJ,WAAYmC,EAAQ/L,QAAWA,EAC/BkM,YAAaH,EAAQpM,SAAYA,EACjCuF,QAAS6G,EAAQ7G,QACjBxD,MAAOqK,EAAQrK,MACf0K,YAAaP,EACbM,UAAWJ,EAAQI,UACnB9G,eAAgB0G,EAAQ1G,gBAG7B,GACD,CAACsG,EAAQ5L,EAAU8L,GAEvB,CAIO,SAASlC,EACd1J,EAAeoM,GAEP,IADRrH,SAAEA,QAA8C,IAAAqH,EAAG,CAAA,EAAEA,GAEjDtM,SAAEA,GAAa+C,EAAM2C,WAAWC,EAAAA,0BAChC4G,EAAexJ,EAAM2C,WAAW8G,EAAYC,qBACtCF,GAAV7B,EAASC,kBAAA,GAET,IAAK+B,GAASH,EAAaI,QAAQC,OAAO,GAGtCvG,EAAIvE,EAAQ4F,CAAAA,EAAAA,EAAeA,gBAACxH,GAAkB,IAAK,CAAE+E,cAKrDe,EAAW2B,EAAAA,cACf,GAAc,MAAVzH,EAAgB,CAGlBmG,EAAKG,OAASR,EAASQ,OAKvB,IAAIqG,EAAS,IAAIjO,gBAAgByH,EAAKG,QAClCsG,EAAcD,EAAOE,OAAO,SAEhC,GADyBD,EAAYE,MAAMzN,GAAY,KAANA,IACzB,CACtBsN,EAAOI,OAAO,SACdH,EAAYhE,QAAQvJ,GAAMA,IAAG2N,SAAS3N,GAAMsN,EAAOxL,OAAO,QAAS9B,KACnE,IAAI4N,EAAKN,EAAOO,WAChB/G,EAAKG,OAAS2G,EAASA,IAAAA,EAAO,EAChC,CACF,CAiBA,OAfMjN,GAAqB,MAAXA,IAAmBwM,EAAMW,MAAMC,QAC7CjH,EAAKG,OAASH,EAAKG,OACfH,EAAKG,OAAOrB,QAAQ,MAAO,WAC3B,UAOW,MAAbnF,IACFqG,EAAKC,SACe,MAAlBD,EAAKC,SAAmBtG,EAAWuN,EAASA,UAAC,CAACvN,EAAUqG,EAAKC,YAG1DkF,EAAAA,WAAWnF,EACpB,CAuHA,MAAMmH,EAAiC,gCACvC,IAAIC,EAA+C,CAAA,EAKnD,SAASC,EAAoBC,GAMrB,IANsBC,OAC5BA,EAAMC,WACNA,QAID,IAAAF,EAAG,CAAA,EAAEA,GACE/B,OAAAA,GAAWvB,EAAqBF,EAAe2D,uBACjDC,sBAAEA,EAAqB1I,mBAAEA,GAAuBuF,EAClDR,EAAoB0D,uBAElB9N,SAAEA,GAAa+C,EAAM2C,WAAWC,EAAAA,0BAChCK,EAAW2B,EAAAA,cACXgF,EAAUqB,EAAAA,aACV3F,EAAa4F,EAAAA,gBAGjBlL,EAAMmL,WAAU,KACd3M,OAAO4M,QAAQC,kBAAoB,SAC5B,KACL7M,OAAO4M,QAAQC,kBAAoB,MAAM,IAE1C,IAqIL,SACEC,EACArC,GAEA,IAAIsC,QAAEA,GAAYtC,GAAW,CAAA,EAC7BjJ,EAAMmL,WAAU,KACd,IAAIK,EAAkB,MAAXD,EAAkB,CAAEA,gBAAYhN,EAE3C,OADAC,OAAOiN,iBAAiB,WAAYH,EAAUE,GACvC,KACLhN,OAAOkN,oBAAoB,WAAYJ,EAAUE,EAAK,CACvD,GACA,CAACF,EAAUC,GAChB,CA9IEI,CACE3L,EAAMiI,aAAY,KAChB,GAAyB,SAArB3C,EAAW1G,MAAkB,CAC/B,IAAIxC,GAAOyO,EAASA,EAAO5H,EAAU2G,GAAW,OAAS3G,EAAS7G,IAClEsO,EAAqBtO,GAAOoC,OAAOoN,OACrC,CACA,IACEC,eAAeC,QACbhB,GAAcL,EACdsB,KAAKC,UAAUtB,GAOnB,CALE,MAAO9K,GAKT,CACApB,OAAO4M,QAAQC,kBAAoB,MAAM,GACxC,CAACP,EAAYD,EAAQvF,EAAW1G,MAAOqE,EAAU2G,KAI9B,oBAAb5L,WAETgC,EAAMiM,iBAAgB,KACpB,IACE,IAAIC,EAAmBL,eAAeM,QACpCrB,GAAcL,GAEZyB,IACFxB,EAAuBqB,KAAKK,MAAMF,GAGpC,CADA,MAAOhO,GACP,IAED,CAAC4M,IAIJ9K,EAAMiM,iBAAgB,KACpB,IAAII,EACFxB,GAAuB,MAAb5N,EACN,CAACgG,EAAU2G,IACTiB,EACE9L,KAEKkE,EAAQ,CACXM,SACE9F,EAAaA,cAACwF,EAASM,SAAUtG,IACjCgG,EAASM,WAEbqG,GAEJiB,EACFyB,EAA2BzD,MAAAA,OAAAA,EAAAA,EAAQ0D,wBACrC7B,GACA,IAAMlM,OAAOoN,SACbS,GAEF,MAAO,IAAMC,GAA4BA,GAA0B,GAClE,CAACzD,EAAQ5L,EAAU4N,IAItB7K,EAAMiM,iBAAgB,KAEpB,IAA8B,IAA1BjB,EAKJ,GAAqC,iBAA1BA,EAAX,CAMA,GAAI/H,EAASS,KAAM,CACjB,IAAI8I,EAAKxO,SAASyO,eAChBC,mBAAmBzJ,EAASS,KAAKmG,MAAM,KAEzC,GAAI2C,EAEF,YADAA,EAAGG,gBAGP,EAG2B,IAAvBrK,GAKJ9D,OAAOoO,SAAS,EAAG,EAnBnB,MAFEpO,OAAOoO,SAAS,EAAG5B,EAqBA,GACpB,CAAC/H,EAAU+H,EAAuB1I,IAEzC,CAgGA,SAAS2C,EACP5C,EACAmJ,QAAwC,IAAxCA,IAAAA,EAA2C,CAAA,GAE3C,IAAIqB,EAAY7M,EAAM2C,WAAW5C,GAGlB,MAAb8M,GADFlF,EAASC,kBAAA,GAMT,IAAI3K,SAAEA,GAAaqK,EACjBF,EAAenC,wBAEb3B,EAAOqB,EAAeA,gBAACtC,EAAI,CAAEH,SAAUsJ,EAAKtJ,WAChD,IAAK2K,EAAU3M,gBACb,OAAO,EAGT,IAAI4M,EACFrP,gBAAcoP,EAAUE,gBAAgBxJ,SAAUtG,IAClD4P,EAAUE,gBAAgBxJ,SACxByJ,EACFvP,gBAAcoP,EAAUI,aAAa1J,SAAUtG,IAC/C4P,EAAUI,aAAa1J,SAezB,OACwC,MAAtC2J,YAAU5J,EAAKC,SAAUyJ,IACgB,MAAzCE,EAASA,UAAC5J,EAAKC,SAAUuJ,EAE7B,2gKApvCO,SAAsBK,GAKN,IALOlQ,SAC5BA,EAAQwH,SACRA,EAAQlD,OACRA,EAAM/C,OACNA,GACmB2O,EACfC,EAAapN,EAAMqN,SACG,MAAtBD,EAAWE,UACbF,EAAWE,QAAUC,uBAAqB,CAAE/O,SAAQgP,UAAU,KAGhE,IAAIpC,EAAUgC,EAAWE,SACpB1O,EAAO6O,GAAgBzN,EAAM0N,SAAS,CACzCvQ,OAAQiO,EAAQjO,OAChB8F,SAAUmI,EAAQnI,YAEhB0K,mBAAEA,GAAuBpM,GAAU,CAAA,EACnCqM,EAAW5N,EAAMiI,aAClB4F,IACCF,GAAsBtN,EAClBA,GAAoB,IAAMoN,EAAaI,KACvCJ,EAAaI,EAAS,GAE5B,CAACJ,EAAcE,IAKjB,OAFA3N,EAAMiM,iBAAgB,IAAMb,EAAQ0C,OAAOF,IAAW,CAACxC,EAASwC,IAG9D5N,EAAA/B,cAAC8P,SAAM,CACL9Q,SAAUA,EACVwH,SAAUA,EACVxB,SAAUrE,EAAMqE,SAChB+K,eAAgBpP,EAAMzB,OACtB6H,UAAWoG,EACX7J,OAAQA,GAGd,wBAaO,SAAmB0M,GAKN,IALOhR,SACzBA,EAAQwH,SACRA,EAAQlD,OACRA,EAAM/C,OACNA,GACgByP,EACZb,EAAapN,EAAMqN,SACG,MAAtBD,EAAWE,UACbF,EAAWE,QAAUY,oBAAkB,CAAE1P,SAAQgP,UAAU,KAG7D,IAAIpC,EAAUgC,EAAWE,SACpB1O,EAAO6O,GAAgBzN,EAAM0N,SAAS,CACzCvQ,OAAQiO,EAAQjO,OAChB8F,SAAUmI,EAAQnI,YAEhB0K,mBAAEA,GAAuBpM,GAAU,CAAA,EACnCqM,EAAW5N,EAAMiI,aAClB4F,IACCF,GAAsBtN,EAClBA,GAAoB,IAAMoN,EAAaI,KACvCJ,EAAaI,EAAS,GAE5B,CAACJ,EAAcE,IAKjB,OAFA3N,EAAMiM,iBAAgB,IAAMb,EAAQ0C,OAAOF,IAAW,CAACxC,EAASwC,IAG9D5N,EAAA/B,cAAC8P,SAAM,CACL9Q,SAAUA,EACVwH,SAAUA,EACVxB,SAAUrE,EAAMqE,SAChB+K,eAAgBpP,EAAMzB,OACtB6H,UAAWoG,EACX7J,OAAQA,GAGd,wCAhYO,SAAuB4M,GAIc,IAJbC,gBAC7BA,EAAevF,OACfA,EAAMtH,OACNA,GACoB4M,GACfvP,EAAO6O,GAAgBzN,EAAM0N,SAAS7E,EAAOjK,QAC7CyP,EAAcC,GAAmBtO,EAAM0N,YACvCb,EAAW0B,GAAgBvO,EAAM0N,SAAsC,CAC1ExN,iBAAiB,KAEdsO,EAAWC,GAAgBzO,EAAM0N,YACjCgB,EAAYC,GAAiB3O,EAAM0N,YACnCkB,EAAcC,GAAmB7O,EAAM0N,WAKxCoB,EAAc9O,EAAMqN,OAAyB,IAAIjN,MACjDuN,mBAAEA,GAAuBpM,GAAU,CAAA,EAEnCwN,EAAuB/O,EAAMiI,aAC9BvH,IACKiN,EAzEV,SAA6BjN,GACvBL,EACFA,EAAoBK,GAEpBA,GAEJ,CAoEQsO,CAAoBtO,GAEpBA,GACF,GAEF,CAACiN,IAGCC,EAAW5N,EAAMiI,aACnB,CACE4F,EAAqBoB,KAMlB,IALHC,gBACEA,EACA7F,UAAWA,EACX8F,mBAAoBA,GACrBF,EAEDC,EAAgB/E,SAAS/N,GAAQ0S,EAAYxB,QAAQpD,OAAO9N,KAC5DyR,EAASuB,SAASjF,SAAQ,CAACkF,EAASjT,UACbmC,IAAjB8Q,EAAQ7P,MACVsP,EAAYxB,QAAQgC,IAAIlT,EAAKiT,EAAQ7P,KACvC,IAGF,IAAI+P,EACe,MAAjB1G,EAAOrK,QACmB,MAA1BqK,EAAOrK,OAAOR,UACwC,mBAA/C6K,EAAOrK,OAAOR,SAASwR,oBAIhC,GAAKL,IAAsBI,EAA3B,CAUA,GAAIlG,EAAW,CAEb5I,GAAc,KAERiO,IACFF,GAAaA,EAAUxN,UACvB0N,EAAWe,kBAEblB,EAAa,CACXrO,iBAAiB,EACjBmJ,WAAW,EACX0D,gBAAiBoC,EAAmBpC,gBACpCE,aAAckC,EAAmBlC,cACjC,IAIJ,IAAIyC,EAAI7G,EAAOrK,OAAQR,SAASwR,qBAAoB,KAClD/O,GAAc,IAAMgN,EAAaI,IAAU,IAc7C,OAVA6B,EAAEC,SAASC,SAAQ,KACjBnP,GAAc,KACZgO,OAAalQ,GACboQ,OAAcpQ,GACd+P,OAAgB/P,GAChBgQ,EAAa,CAAErO,iBAAiB,GAAQ,GACxC,SAGJO,GAAc,IAAMkO,EAAce,IAEpC,CAGIhB,GAGFF,GAAaA,EAAUxN,UACvB0N,EAAWe,iBACXZ,EAAgB,CACdjQ,MAAOiP,EACPd,gBAAiBoC,EAAmBpC,gBACpCE,aAAckC,EAAmBlC,iBAInCqB,EAAgBT,GAChBU,EAAa,CACXrO,iBAAiB,EACjBmJ,WAAW,EACX0D,gBAAiBoC,EAAmBpC,gBACpCE,aAAckC,EAAmBlC,eAxDrC,MANM5D,EACF5I,GAAc,IAAMgN,EAAaI,KAEjCkB,GAAqB,IAAMtB,EAAaI,IA6D5C,GAEF,CAAChF,EAAOrK,OAAQkQ,EAAYF,EAAWM,EAAaC,IAKtD/O,EAAMiM,iBAAgB,IAAMpD,EAAOgH,UAAUjC,IAAW,CAAC/E,EAAQ+E,IAIjE5N,EAAMmL,WAAU,KACV0B,EAAU3M,kBAAoB2M,EAAUxD,WAC1CoF,EAAa,IAAI9N,EACnB,GACC,CAACkM,IAKJ7M,EAAMmL,WAAU,KACd,GAAIqD,GAAaH,GAAgBxF,EAAOrK,OAAQ,CAC9C,IAAIqP,EAAWQ,EACXyB,EAAgBtB,EAAU1N,QAC1B4N,EAAa7F,EAAOrK,OAAOR,SAASwR,qBAAoBO,UAC1DhB,GAAqB,IAAMtB,EAAaI,WAClCiC,CAAa,IAErBpB,EAAWiB,SAASC,SAAQ,KAC1BnB,OAAalQ,GACboQ,OAAcpQ,GACd+P,OAAgB/P,GAChBgQ,EAAa,CAAErO,iBAAiB,GAAQ,IAE1CyO,EAAcD,EAChB,IACC,CAACK,EAAsBV,EAAcG,EAAW3F,EAAOrK,SAI1DwB,EAAMmL,WAAU,KAEZqD,GACAH,GACAzP,EAAMqE,SAAS7G,MAAQiS,EAAapL,SAAS7G,KAE7CoS,EAAUxN,SACZ,GACC,CAACwN,EAAWE,EAAY9P,EAAMqE,SAAUoL,IAI3CrO,EAAMmL,WAAU,MACT0B,EAAU3M,iBAAmB0O,IAChCN,EAAgBM,EAAahQ,OAC7B2P,EAAa,CACXrO,iBAAiB,EACjBmJ,WAAW,EACX0D,gBAAiB6B,EAAa7B,gBAC9BE,aAAc2B,EAAa3B,eAE7B4B,OAAgBtQ,GAClB,GACC,CAACsO,EAAU3M,gBAAiB0O,IAE/B5O,EAAMmL,WAAU,QAQb,IAEH,IAAInG,EAAYhF,EAAMgQ,SAAQ,KACrB,CACLC,WAAYpH,EAAOoH,WACnB9K,eAAgB0D,EAAO1D,eACvB+K,GAAKC,GAAMtH,EAAOvC,SAAS6J,GAC3BC,KAAMA,CAAC/N,EAAIzD,EAAO4M,IAChB3C,EAAOvC,SAASjE,EAAI,CAClBzD,QACA0D,mBAAoBkJ,MAAAA,OAAAA,EAAAA,EAAMlJ,qBAE9BF,QAASA,CAACC,EAAIzD,EAAO4M,IACnB3C,EAAOvC,SAASjE,EAAI,CAClBD,SAAS,EACTxD,QACA0D,mBAAoBkJ,MAAAA,OAAAA,EAAAA,EAAMlJ,wBAG/B,CAACuG,IAEA5L,EAAW4L,EAAO5L,UAAY,IAE9BoT,EAAoBrQ,EAAMgQ,SAC5B,KAAO,CACLnH,SACA7D,YACAsL,QAAQ,EACRrT,cAEF,CAAC4L,EAAQ7D,EAAW/H,IAGlBsT,EAAevQ,EAAMgQ,SACvB,KAAO,CACLQ,qBAAsB3H,EAAOtH,OAAOiP,wBAEtC,CAAC3H,EAAOtH,OAAOiP,uBASjB,OACExQ,EAAA/B,cAAA+B,EAAAyQ,SACEzQ,KAAAA,EAAA/B,cAACwJ,EAAiBC,yBAACgJ,SAAQ,CAACrU,MAAOgU,GACjCrQ,EAAA/B,cAAC6G,EAAsBC,8BAAC2L,SAAQ,CAACrU,MAAOuC,GACtCoB,EAAA/B,cAACkC,EAAgBuQ,SAAQ,CAACrU,MAAOyS,EAAYxB,SAC3CtN,EAAA/B,cAAC8B,EAAsB2Q,SAAQ,CAACrU,MAAOwQ,GACrC7M,EAAA/B,cAAC8P,SAAM,CACL9Q,SAAUA,EACVgG,SAAUrE,EAAMqE,SAChB+K,eAAgBpP,EAAM+R,cACtB3L,UAAWA,EACXzD,OAAQgP,GAEP3R,EAAMgS,aAAe/H,EAAOtH,OAAOsP,oBAClC7Q,EAAA/B,cAACkD,EAAkB,CACjBG,OAAQuH,EAAOvH,OACfC,OAAQsH,EAAOtH,OACf3C,MAAOA,IAGTwP,OAOX,KAGP,sBAilBO,SAA0B0C,GAGN,IAHOjG,OAChCA,EAAMC,WACNA,GACuBgG,EAEvB,OADAnG,EAAqB,CAAEE,SAAQC,eACxB,IACT,oHA/jCO,SACLxJ,EACAkK,GAEA,OAAOuF,eAAa,CAClB9T,SAAUuO,MAAAA,OAAAA,EAAAA,EAAMvO,SAChBsE,OAAMxC,EAAA,CAAA,EACDyM,MAAAA,OAAAA,EAAAA,EAAMjK,OAAM,CACfyP,oBAAoB,IAEtB5F,QAASmC,EAAAA,qBAAqB,CAAE/O,OAAQgN,MAAAA,OAAAA,EAAAA,EAAMhN,SAC9CyS,eAAezF,MAAAA,OAAAA,EAAAA,EAAMyF,gBAAiBvS,IACtC4C,4BACA4P,EAAkBC,0BAClBC,aAAc5F,MAAAA,OAAAA,EAAAA,EAAM4F,aACpBC,wBAAyB7F,MAAAA,OAAAA,EAAAA,EAAM6F,wBAC/B7S,OAAQgN,MAAAA,OAAAA,EAAAA,EAAMhN,SACb8S,YACL,qBAEO,SACLhQ,EACAkK,GAEA,OAAOuF,eAAa,CAClB9T,SAAUuO,MAAAA,OAAAA,EAAAA,EAAMvO,SAChBsE,OAAMxC,EAAA,CAAA,EACDyM,MAAAA,OAAAA,EAAAA,EAAMjK,OAAM,CACfyP,oBAAoB,IAEtB5F,QAAS8C,EAAAA,kBAAkB,CAAE1P,OAAQgN,MAAAA,OAAAA,EAAAA,EAAMhN,SAC3CyS,eAAezF,MAAAA,OAAAA,EAAAA,EAAMyF,gBAAiBvS,IACtC4C,4BACA4P,EAAkBC,0BAClBC,aAAc5F,MAAAA,OAAAA,EAAAA,EAAM4F,aACpBC,wBAAyB7F,MAAAA,OAAAA,EAAAA,EAAM6F,wBAC/B7S,OAAQgN,MAAAA,OAAAA,EAAAA,EAAMhN,SACb8S,YACL,kDAqkBA,SAAsBC,GAKC,IALAtU,SACrBA,EAAQwH,SACRA,EAAQlD,OACRA,EAAM6J,QACNA,GACmBmG,GACd3S,EAAO6O,GAAgBzN,EAAM0N,SAAS,CACzCvQ,OAAQiO,EAAQjO,OAChB8F,SAAUmI,EAAQnI,YAEhB0K,mBAAEA,GAAuBpM,GAAU,CAAA,EACnCqM,EAAW5N,EAAMiI,aAClB4F,IACCF,GAAsBtN,EAClBA,GAAoB,IAAMoN,EAAaI,KACvCJ,EAAaI,EAAS,GAE5B,CAACJ,EAAcE,IAKjB,OAFA3N,EAAMiM,iBAAgB,IAAMb,EAAQ0C,OAAOF,IAAW,CAACxC,EAASwC,IAG9D5N,EAAA/B,cAAC8P,SAAM,CACL9Q,SAAUA,EACVwH,SAAUA,EACVxB,SAAUrE,EAAMqE,SAChB+K,eAAgBpP,EAAMzB,OACtB6H,UAAWoG,EACX7J,OAAQA,GAGd,uBAwhCA,SAAkBiQ,GAMf,IANgBC,KACjBA,EAAI5R,QACJA,GAID2R,EACKE,EAAUC,aAAWF,GAEzBzR,EAAMmL,WAAU,KACd,GAAsB,YAAlBuG,EAAQ9S,MAAqB,CACjBJ,OAAOoT,QAAQ/R,GAK3BgS,WAAWH,EAAQI,QAAS,GAE5BJ,EAAQK,OAEZ,IACC,CAACL,EAAS7R,IAEbG,EAAMmL,WAAU,KACQ,YAAlBuG,EAAQ9S,OAAwB6S,GAClCC,EAAQK,OACV,GACC,CAACL,EAASD,GACf,oBAxEO,SACLnG,EACArC,GAEA,IAAIsC,QAAEA,GAAYtC,GAAW,CAAA,EAC7BjJ,EAAMmL,WAAU,KACd,IAAIK,EAAkB,MAAXD,EAAkB,CAAEA,gBAAYhN,EAE3C,OADAC,OAAOiN,iBAAiB,eAAgBH,EAAUE,GAC3C,KACLhN,OAAOkN,oBAAoB,eAAgBJ,EAAUE,EAAK,CAC3D,GACA,CAACF,EAAUC,GAChB,eA9PO,SAAmByG,GAE8B,IAAAC,EAAA,IAFhB7V,IACtCA,QACiB,IAAA4V,EAAG,CAAA,EAAEA,GAChBnJ,OAAAA,GAAWvB,EAAqBF,EAAe8K,YACjDtT,EAAQiJ,EAAmBR,EAAoB6K,YAC/CpD,EAAc9O,EAAM2C,WAAWxC,GAC/BmK,EAAQtK,EAAM2C,WAAW8G,EAAYC,qBACrCyI,SAAOF,EAAG3H,EAAMV,QAAQU,EAAMV,QAAQnE,OAAS,WAArCwM,EAAyC3H,MAAM8H,GAEnDtD,GAAVnH,EAASC,kBAAA,GACC0C,GAAV3C,EAASC,kBAAA,GAEI,MAAXuK,GADFxK,EAASC,kBAAA,GAQT,IAAIyK,EAAa7R,EAAYA,IAAc,IACtC6F,EAAYiM,GAAiBtS,EAAM0N,SAAiBtR,GAAOiW,GAC5DjW,GAAOA,IAAQiK,EACjBiM,EAAclW,GACJiK,GAEViM,EAAc3J,KAIhB3I,EAAMmL,WAAU,KACdtC,EAAO0J,WAAWlM,GACX,KAILwC,EAAO2J,cAAcnM,EAAW,IAEjC,CAACwC,EAAQxC,IAGZ,IAAIoM,EAAOzS,EAAMiI,aACf,CAAC/E,EAAcsI,KACH2G,GAAVxK,EAASC,kBAAA,GACTiB,EAAOM,MAAM9C,EAAY8L,EAASjP,EAAMsI,EAAK,GAE/C,CAACnF,EAAY8L,EAAStJ,IAGpB6J,EAAa/L,IACbD,EAAS1G,EAAMiI,aACjB,CAACjL,EAAQwO,KACPkH,EAAW1V,EAAM+B,KACZyM,EAAI,CACPlF,UAAU,EACVD,eACA,GAEJ,CAACA,EAAYqM,IAGXC,EAAc3S,EAAMgQ,SAAQ,IACZhQ,EAAM6B,YACtB,CAAC2E,EAAOzE,IAEJ/B,EAAA/B,cAACiI,EAAInH,KAAKyH,EAAK,CAAEF,UAAU,EAAOD,WAAYA,EAAYtE,IAAKA,QAQpE,CAACsE,IAGAgJ,EAAUzQ,EAAMwQ,SAASwD,IAAIvM,IAAewM,eAC5CrT,EAAOsP,EAAY8D,IAAIvM,GAY3B,OAX4BrG,EAAMgQ,SAChC,IAAAjR,EAAA,CACEmH,KAAMyM,EACNjM,SACA+L,QACGpD,EAAO,CACV7P,UAEF,CAACmT,EAAajM,EAAQ+L,EAAMpD,EAAS7P,GAIzC,gBAMO,WACL,IAAIZ,EAAQiJ,EAAmBR,EAAoByL,aACnD,OAAOhX,MAAMiX,KAAKnU,EAAMwQ,SAASnQ,WAAW1C,KAAIyW,IAAA,IAAE5W,EAAKiT,GAAQ2D,EAAA,OAAAjU,KAC1DsQ,EAAO,CACVjT,OAAG,GAEP,8DAzTO,SACL6W,GAUA,IAAIC,EAAyBlT,EAAMqN,OAAO1R,EAAmBsX,IACzDE,EAAwBnT,EAAMqN,QAAO,GAErCpK,EAAW2B,EAAAA,cACXwO,EAAepT,EAAMgQ,SACvB,ID/2CG,SACLqD,EACAC,GAEA,IAAIF,EAAezX,EAAmB0X,GAiBtC,OAfIC,GAMFA,EAAoBnJ,SAAQ,CAACoJ,EAAGnX,KACzBgX,EAAatW,IAAIV,IACpBkX,EAAoBtJ,OAAO5N,GAAK+N,SAAS9N,IACvC+W,EAAa9U,OAAOlC,EAAKC,EAAM,GAEnC,IAIG+W,CACT,CC61CMI,CACEvQ,EAASQ,OACT0P,EAAsB7F,QAAU,KAAO4F,EAAuB5F,UAElE,CAACrK,EAASQ,SAGR6C,EAAW0B,EAAAA,cACXyL,EAAkBzT,EAAMiI,aAC1B,CAACyL,EAAUC,KACT,MAAMC,EAAkBjY,EACF,mBAAb+X,EAA0BA,EAASN,GAAgBM,GAE5DP,EAAsB7F,SAAU,EAChChH,EAAS,IAAMsN,EAAiBD,EAAgB,GAElD,CAACrN,EAAU8M,IAGb,MAAO,CAACA,EAAcK,EACxB"}