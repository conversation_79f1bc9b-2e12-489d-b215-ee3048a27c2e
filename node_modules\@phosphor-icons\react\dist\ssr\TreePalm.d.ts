import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMzcuNzksNTMuMjNhNjYuODYsNjYuODYsMCwwLDAtOTcuNzQsMCw3Mi4yMSw3Mi4yMSwwLDAsMC0xMi4wNSwxNyw3Mi4yMSw3Mi4yMSwwLDAsMC0xMi0xNyw2Ni44Niw2Ni44NiwwLDAsMC05Ny43NCwwLDgsOCwwLDAsMCwyLjYsMTIuODVMNzcsOTAuNTVhNzEuNDIsNzEuNDIsMCwwLDAtNDMuMzYsMzMuMjEsNzAuNjQsNzAuNjQsMCwwLDAtNy4yLDU0LjMyQTgsOCwwLDAsMCwzOSwxODIuMzZsODEtNjEuNjhWMjI0YTgsOCwwLDAsMCwxNiwwVjEyMC42OGw4MSw2MS42OGE4LDgsMCwwLDAsMTIuNTctNC4yOCw3MC42NCw3MC42NCwwLDAsMC03LjItNTQuMzJBNzEuNDIsNzEuNDIsMCwwLDAsMTc5LDkwLjU1bDU2LjIyLTI0LjQ3YTgsOCwwLDAsMCwyLjYtMTIuODVaTTY3LjA4LDQ4YTUxLjEzLDUxLjEzLDAsMCwxLDM3LjI4LDE2LjI2LDU2LjUzLDU2LjUzLDAsMCwxLDE0LjI2LDI2LjkzTDM5LDU2LjUzQTUwLjUsNTAuNSwwLDAsMSw2Ny4wOCw0OFpNNDAsMTYxLjVhNTQuODIsNTQuODIsMCwwLDEsNy40Ny0yOS43LDU1LjU1LDU1LjU1LDAsMCwxLDM0LTI1Ljg5QTU2LjUyLDU2LjUyLDAsMCwxLDk2LjEsMTA0YTU1LjgyLDU1LjgyLDAsMCwxLDE2LjIzLDIuNDFaTTIwOC41LDEzMS44QTU0LjgyLDU0LjgyLDAsMCwxLDIxNiwxNjEuNWwtNzIuMy01NS4xYTU2LjMsNTYuMywwLDAsMSw2NC44MywyNS40Wk0xMzcuMzgsOTEuMTlhNTYuNTMsNTYuNTMsMCwwLDEsMTQuMjYtMjYuOTNBNTEuMTMsNTEuMTMsMCwwLDEsMTg4LjkyLDQ4LDUwLjUsNTAuNSwwLDAsMSwyMTcsNTYuNTNaIi8+PC9zdmc+)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMzQuOSw1NmE2Mi44Niw2Mi44NiwwLDAsMC05MiwwQTY4LjE2LDY4LjE2LDAsMCwwLDEyOCw4MC40NGE2OC4xNiw2OC4xNiwwLDAsMC0xNS0yNC40NUE2Mi44Niw2Mi44NiwwLDAsMCwyMS4xLDU2YTQsNCwwLDAsMCwxLjMsNi40Mkw5MC44Niw5Mi4yYTY4LjY2LDY4LjY2LDAsMCwwLTEyLjQ1LDIuMTEsNjcuNDksNjcuNDksMCwwLDAtNDEuMjgsMzEuNDZBNjYuNzIsNjYuNzIsMCwwLDAsMzAuMzMsMTc3YTQsNCwwLDAsMCwyLjU3LDIuNzUsNC4xLDQuMSwwLDAsMCwxLjI5LjIxLDQsNCwwLDAsMCwyLjQzLS44MkwxMjQsMTEyLjZWMjI0YTQsNCwwLDAsMCw4LDBWMTEyLjZsODcuMzgsNjYuNThhNCw0LDAsMCwwLDIuNDMuODIsNC4xLDQuMSwwLDAsMCwxLjI5LS4yMSw0LDQsMCwwLDAsMi41Ny0yLjc1LDY2LjcyLDY2LjcyLDAsMCwwLTYuOC01MS4yNyw2Ny40OSw2Ny40OSwwLDAsMC00MS4yOC0zMS40Niw2OC42Niw2OC42NiwwLDAsMC0xMi40NS0yLjExTDIzMy42LDYyLjQxQTQsNCwwLDAsMCwyMzQuOSw1NlpNNjcuMDgsNDRhNTUuMTMsNTUuMTMsMCwwLDEsNDAuMTgsMTcuNUE2MC44Niw2MC44NiwwLDAsMSwxMjMuNyw5Ny43N0wzMS4xMiw1Ny40OEE1NC43NSw1NC43NSwwLDAsMSw2Ny4wOCw0NFpNMzYuNzcsMTY5QTU4LjcyLDU4LjcyLDAsMCwxLDQ0LDEyOS43OSw1OS41Nyw1OS41NywwLDAsMSw4MC40NywxMDIsNjEsNjEsMCwwLDEsOTYuMSwxMDBhNTkuNzEsNTkuNzEsMCwwLDEsMjQuNDEsNS4yMlpNMjEyLDEyOS43OUE1OC43Miw1OC43MiwwLDAsMSwyMTkuMjMsMTY5TDEzNS40OSwxMDUuMkE2MC4yNCw2MC4yNCwwLDAsMSwyMTIsMTI5Ljc5Wm0tNzkuNjYtMzJBNjAuODYsNjAuODYsMCwwLDEsMTQ4Ljc0LDYxLjVhNTQuOCw1NC44LDAsMCwxLDc2LjE0LTRaIi8+PC9zdmc+)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMzYuMzUsNTQuNjFhNjQuODcsNjQuODcsMCwwLDAtOTQuODUsMEE3MCw3MCwwLDAsMCwxMjgsNzVhNzAsNzAsMCwwLDAtMTMuNS0yMC4zNyw2NC44Nyw2NC44NywwLDAsMC05NC44NSwwLDYsNiwwLDAsMCwyLDkuNjRsNjEuODMsMjYuOWMtMS44NS4zNC0zLjcuNzQtNS41NCwxLjIzYTY5LjQyLDY5LjQyLDAsMCwwLTQyLjUsMzIuMzksNjguNjUsNjguNjUsMCwwLDAtNyw1Mi43OSw2LDYsMCwwLDAsMy44Niw0LjEyLDYuMSw2LjEsMCwwLDAsMS45My4zMiw2LDYsMCwwLDAsMy42NC0xLjIzTDEyMiwxMTYuNjRWMjI0YTYsNiwwLDAsMCwxMiwwVjExNi42NGw4NC4xNyw2NC4xM2E2LDYsMCwwLDAsMy42NCwxLjIzLDYuMSw2LjEsMCwwLDAsMS45My0uMzIsNiw2LDAsMCwwLDMuODYtNC4xMiw2OC42NSw2OC42NSwwLDAsMC03LTUyLjc5LDY5LjQyLDY5LjQyLDAsMCwwLTQyLjUtMzIuMzljLTEuODQtLjQ5LTMuNjktLjg5LTUuNTQtMS4yM2w2MS44My0yNi45YTYsNiwwLDAsMCwyLTkuNjRaTTY3LjA4LDQ2YTUzLjE2LDUzLjE2LDAsMCwxLDM4LjczLDE2Ljg4LDU4Ljc3LDU4Ljc3LDAsMCwxLDE1LjQ3LDMxLjY1TDM0LjkzLDU3QTUyLjU5LDUyLjU5LDAsMCwxLDY3LjA4LDQ2Wk0zOC4yOSwxNjUuMzNhNTYuNzcsNTYuNzcsMCwwLDEsNy40OC0zNC41M0E1Ny41OCw1Ny41OCwwLDAsMSw4MSwxMDRhNTguNzksNTguNzksMCwwLDEsMTUuMTItMiw1Ny42Nyw1Ny42NywwLDAsMSwyMC40MywzLjczWk0yMTAuMjMsMTMwLjhhNTYuNzcsNTYuNzcsMCwwLDEsNy40OCwzNC41M2wtNzguMjQtNTkuNjFhNTguMjQsNTguMjQsMCwwLDEsNzAuNzYsMjUuMDhaTTEzNC43Miw5NC41M2E1OC43Nyw1OC43NywwLDAsMSwxNS40Ny0zMS42NUE1My4xNiw1My4xNiwwLDAsMSwxODguOTIsNDZhNTIuNTksNTIuNTksMCwwLDEsMzIuMTUsMTFaIi8+PC9zdmc+)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDAuNjksNTAuNDdhNzAuODcsNzAuODcsMCwwLDAtMTAzLjU0LDBBNzYuNTgsNzYuNTgsMCwwLDAsMTI4LDYyLjE5YTc2LjU4LDc2LjU4LDAsMCwwLTkuMTUtMTEuNzIsNzAuODcsNzAuODcsMCwwLDAtMTAzLjU0LDAsMTIsMTIsMCwwLDAsMy45LDE5LjI4TDY2LDkwLjEyYTc1LjQ1LDc1LjQ1LDAsMCwwLTQzLjQzLDg5LDEyLDEyLDAsMCwwLDE4Ljg1LDYuNDFMMTE2LDEyOC43NVYyMjRhMTIsMTIsMCwwLDAsMjQsMFYxMjguNzVsNzQuNTQsNTYuNzlhMTIsMTIsMCwwLDAsMTguODUtNi40MSw3NS40NSw3NS40NSwwLDAsMC00My40My04OWw0Ni44My0yMC4zN2ExMiwxMiwwLDAsMCwzLjktMTkuMjhaTTY3LjA4LDUyYTQ3LjE2LDQ3LjE2LDAsMCwxLDM0LjM4LDE1QTUyLjQxLDUyLjQxLDAsMCwxLDExMi4zLDg0LjA4TDQ4LDU2LjFBNDYuMjgsNDYuMjgsMCwwLDEsNjcuMDgsNTJaTTQ0LjM5LDE1My4xNWE1MS43Miw1MS43MiwwLDAsMSwzOC4xNC00My4zOEE1Mi44Myw1Mi44MywwLDAsMSw5Ni4wOSwxMDhhNTAuNCw1MC40LDAsMCwxLDcsLjQ3Wk0yMDUsMTMzLjgxYTUxLjE0LDUxLjE0LDAsMCwxLDYuNTcsMTkuMzRMMTUzLDEwOC40NmE1Mi4yMSw1Mi4yMSwwLDAsMSwyMC41MSwxLjMxQTUxLjYxLDUxLjYxLDAsMCwxLDIwNSwxMzMuODFaTTE0My43LDg0LjA4QTUyLjQxLDUyLjQxLDAsMCwxLDE1NC41NCw2N2E0Ny4xNiw0Ny4xNiwwLDAsMSwzNC4zOC0xNUE0Ni4yOCw0Ni4yOCwwLDAsMSwyMDgsNTYuMVoiLz48L3N2Zz4=)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMzkuODQsNjAuMzNhOCw4LDAsMCwxLTQuNjUsNS43NUwxNzksOTAuNTVhNzEuNDIsNzEuNDIsMCwwLDEsNDMuMzYsMzMuMjEsNzAuNjQsNzAuNjQsMCwwLDEsNy4yLDU0LjMyQTgsOCwwLDAsMSwyMTcsMTgyLjM2bC04MS02MS42OFYyMjRhOCw4LDAsMCwxLTE2LDBWMTIwLjY4TDM5LDE4Mi4zNmE4LDgsMCwwLDEtMTIuNTctNC4yOCw3MC42NCw3MC42NCwwLDAsMSw3LjItNTQuMzJBNzEuNDIsNzEuNDIsMCwwLDEsNzcsOTAuNTVMMjAuODEsNjYuMDhhOCw4LDAsMCwxLTIuNi0xMi44NSw2Ni44Niw2Ni44NiwwLDAsMSw5Ny43NCwwLDcyLjIxLDcyLjIxLDAsMCwxLDEyLDE3LDcyLjIxLDcyLjIxLDAsMCwxLDEyLjA1LTE3LDY2Ljg2LDY2Ljg2LDAsMCwxLDk3Ljc0LDBBOCw4LDAsMCwxLDIzOS44NCw2MC4zM1oiLz48L3N2Zz4=)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMzIsNTguNzUsMTI4LDEwNGE2NS40Nyw2NS40NywwLDAsMSwxNy44NC00NS4yNSw1OC44Nyw1OC44NywwLDAsMSw4Ni4xNiwwWk0xNzYuNTYsOTguMThBNjQuMiw2NC4yLDAsMCwwLDEyOCwxMDQuNTJMMjIxLjgxLDE3NmE2Myw2MywwLDAsMC02LjM5LTQ4LjIyQTYzLjczLDYzLjczLDAsMCwwLDE3Ni41Niw5OC4xOFptLTY2LjQtMzkuNDNhNTguODcsNTguODcsMCwwLDAtODYuMTYsMEwxMjgsMTA0QTY1LjQ3LDY1LjQ3LDAsMCwwLDExMC4xNiw1OC43NVpNNzkuNDQsOTguMThhNjMuNzMsNjMuNzMsMCwwLDAtMzguODYsMjkuNkE2Myw2MywwLDAsMCwzNC4xOSwxNzZMMTI4LDEwNC41MkE2NC4yLDY0LjIsMCwwLDAsNzkuNDQsOTguMThaIiBvcGFjaXR5PSIwLjIiLz48cGF0aCBkPSJNMjM3Ljc5LDUzLjIzYTY2Ljg2LDY2Ljg2LDAsMCwwLTk3Ljc0LDAsNzIuMjEsNzIuMjEsMCwwLDAtMTIuMDUsMTcsNzIuMjEsNzIuMjEsMCwwLDAtMTItMTcsNjYuODYsNjYuODYsMCwwLDAtOTcuNzQsMCw4LDgsMCwwLDAsMi42LDEyLjg1TDc3LDkwLjU1YTcxLjQyLDcxLjQyLDAsMCwwLTQzLjM2LDMzLjIxLDcwLjY0LDcwLjY0LDAsMCwwLTcuMiw1NC4zMkE4LDgsMCwwLDAsMzksMTgyLjM2bDgxLTYxLjY4VjIyNGE4LDgsMCwwLDAsMTYsMFYxMjAuNjhsODEsNjEuNjhhOCw4LDAsMCwwLDEyLjU3LTQuMjgsNzAuNjQsNzAuNjQsMCwwLDAtNy4yLTU0LjMyQTcxLjQyLDcxLjQyLDAsMCwwLDE3OSw5MC41NWw1Ni4yMi0yNC40N2E4LDgsMCwwLDAsMi42LTEyLjg1Wk02Ny4wOCw0OGE1MS4xMyw1MS4xMywwLDAsMSwzNy4yOCwxNi4yNiw1Ni41Myw1Ni41MywwLDAsMSwxNC4yNiwyNi45M0wzOSw1Ni41M0E1MC41LDUwLjUsMCwwLDEsNjcuMDgsNDhaTTQwLDE2MS41YTU0LjgyLDU0LjgyLDAsMCwxLDcuNDctMjkuNyw1NS41NSw1NS41NSwwLDAsMSwzNC0yNS44OUE1Ni41Miw1Ni41MiwwLDAsMSw5Ni4xLDEwNGE1NS44Miw1NS44MiwwLDAsMSwxNi4yMywyLjQxWk0yMDguNSwxMzEuOEE1NC44Miw1NC44MiwwLDAsMSwyMTYsMTYxLjVsLTcyLjMtNTUuMWE1Ni4zLDU2LjMsMCwwLDEsNjQuODMsMjUuNFpNMTM3LjM4LDkxLjE5YTU2LjUzLDU2LjUzLDAsMCwxLDE0LjI2LTI2LjkzQTUxLjEzLDUxLjEzLDAsMCwxLDE4OC45Miw0OCw1MC41LDUwLjUsMCwwLDEsMjE3LDU2LjUzWiIvPjwvc3ZnPg==)
 */
declare const I: Icon;
/** @deprecated Use TreePalmIcon */
export declare const TreePalm: Icon;
export { I as TreePalmIcon };
