import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0zMiw2NGE4LDgsMCwwLDEsOC04SDIxNmE4LDgsMCwwLDEsMCwxNkg0MEE4LDgsMCwwLDEsMzIsNjRabTgsNzJoNzJhOCw4LDAsMCwwLDAtMTZINDBhOCw4LDAsMCwwLDAsMTZabTg4LDQ4SDQwYTgsOCwwLDAsMCwwLDE2aDg4YTgsOCwwLDAsMCwwLTE2Wm0xMDkuNjYsMTMuNjZhOCw4LDAsMCwxLTExLjMyLDBMMjA2LDE3Ny4zNkE0MCw0MCwwLDEsMSwyMTcuMzYsMTY2bDIwLjMsMjAuM0E4LDgsMCwwLDEsMjM3LjY2LDE5Ny42NlpNMTg0LDE2OGEyNCwyNCwwLDEsMC0yNC0yNEEyNCwyNCwwLDAsMCwxODQsMTY4WiIvPjwvc3ZnPg==)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0zNiw2NGE0LDQsMCwwLDEsNC00SDIxNmE0LDQsMCwwLDEsMCw4SDQwQTQsNCwwLDAsMSwzNiw2NFptNCw2OGg3MmE0LDQsMCwwLDAsMC04SDQwYTQsNCwwLDAsMCwwLDhabTg4LDU2SDQwYTQsNCwwLDAsMCwwLDhoODhhNCw0LDAsMCwwLDAtOFptMTA2LjgzLDYuODNhNCw0LDAsMCwxLTUuNjYsMGwtMjIuNzItMjIuNzJhMzYuMDYsMzYuMDYsMCwxLDEsNS42Ni01LjY2bDIyLjcyLDIyLjcyQTQsNCwwLDAsMSwyMzQuODMsMTk0LjgzWk0xODQsMTcyYTI4LDI4LDAsMSwwLTI4LTI4QTI4LDI4LDAsMCwwLDE4NCwxNzJaIi8+PC9zdmc+)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0zNCw2NGE2LDYsMCwwLDEsNi02SDIxNmE2LDYsMCwwLDEsMCwxMkg0MEE2LDYsMCwwLDEsMzQsNjRabTYsNzBoNzJhNiw2LDAsMCwwLDAtMTJINDBhNiw2LDAsMCwwLDAsMTJabTg4LDUySDQwYTYsNiwwLDAsMCwwLDEyaDg4YTYsNiwwLDAsMCwwLTEyWm0xMDguMjQsMTAuMjRhNiw2LDAsMCwxLTguNDgsMGwtMjEuNDktMjEuNDhhMzguMDYsMzguMDYsMCwxLDEsOC40OS04LjQ5bDIxLjQ4LDIxLjQ5QTYsNiwwLDAsMSwyMzYuMjQsMTk2LjI0Wk0xODQsMTcwYTI2LDI2LDAsMSwwLTI2LTI2QTI2LDI2LDAsMCwwLDE4NCwxNzBaIi8+PC9zdmc+)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yOCw2NEExMiwxMiwwLDAsMSw0MCw1MkgyMTZhMTIsMTIsMCwwLDEsMCwyNEg0MEExMiwxMiwwLDAsMSwyOCw2NFptMTIsNzZoNjRhMTIsMTIsMCwwLDAsMC0yNEg0MGExMiwxMiwwLDAsMCwwLDI0Wm04MCw0MEg0MGExMiwxMiwwLDAsMCwwLDI0aDgwYTEyLDEyLDAsMCwwLDAtMjRabTEyMC40OSwyMC40OWExMiwxMiwwLDAsMS0xNywwbC0xOC4wOC0xOC4wOGE0NCw0NCwwLDEsMSwxNy0xN2wxOC4wOCwxOC4wN0ExMiwxMiwwLDAsMSwyNDAuNDksMjAwLjQ5Wk0xODQsMTY0YTIwLDIwLDAsMSwwLTIwLTIwQTIwLDIwLDAsMCwwLDE4NCwxNjRaIi8+PC9zdmc+)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0zMiw2NGE4LDgsMCwwLDEsOC04SDIxNmE4LDgsMCwwLDEsMCwxNkg0MEE4LDgsMCwwLDEsMzIsNjRabTgsNzJoNzJhOCw4LDAsMCwwLDAtMTZINDBhOCw4LDAsMCwwLDAsMTZabTg4LDQ4SDQwYTgsOCwwLDAsMCwwLDE2aDg4YTgsOCwwLDAsMCwwLTE2Wm0xMDkuNjYsMi4zNEwyMTcuMzYsMTY2QTQwLDQwLDAsMSwwLDIwNiwxNzcuMzZsMjAuMywyMC4zYTgsOCwwLDAsMCwxMS4zMi0xMS4zMloiLz48L3N2Zz4=)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMTYsMTQ0YTMyLDMyLDAsMSwxLTMyLTMyQTMyLDMyLDAsMCwxLDIxNiwxNDRaIiBvcGFjaXR5PSIwLjIiLz48cGF0aCBkPSJNMzIsNjRhOCw4LDAsMCwxLDgtOEgyMTZhOCw4LDAsMCwxLDAsMTZINDBBOCw4LDAsMCwxLDMyLDY0Wm04LDcyaDcyYTgsOCwwLDAsMCwwLTE2SDQwYTgsOCwwLDAsMCwwLDE2Wm04OCw0OEg0MGE4LDgsMCwwLDAsMCwxNmg4OGE4LDgsMCwwLDAsMC0xNlptMTA5LjY2LDEzLjY2YTgsOCwwLDAsMS0xMS4zMiwwTDIwNiwxNzcuMzZBNDAsNDAsMCwxLDEsMjE3LjM2LDE2NmwyMC4zLDIwLjNBOCw4LDAsMCwxLDIzNy42NiwxOTcuNjZaTTE4NCwxNjhhMjQsMjQsMCwxLDAtMjQtMjRBMjQsMjQsMCwwLDAsMTg0LDE2OFoiLz48L3N2Zz4=)
 */
declare const I: Icon;
/** @deprecated Use ListMagnifyingGlassIcon */
export declare const ListMagnifyingGlass: Icon;
export { I as ListMagnifyingGlassIcon };
