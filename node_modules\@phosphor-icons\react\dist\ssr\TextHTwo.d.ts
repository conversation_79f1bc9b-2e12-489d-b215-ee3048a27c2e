import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNTIsNTZWMTc2YTgsOCwwLDAsMS0xNiwwVjEyNEg0OHY1MmE4LDgsMCwwLDEtMTYsMFY1NmE4LDgsMCwwLDEsMTYsMHY1Mmg4OFY1NmE4LDgsMCwwLDEsMTYsMFptODgsMTQ0SDIwOGwzMy41NS00NC43NGEzMiwzMiwwLDEsMC01NS43My0yOS45Myw4LDgsMCwxLDAsMTUuMDgsNS4zNCwxNi4yOCwxNi4yOCwwLDAsMSwyLjMyLTQuMywxNiwxNiwwLDEsMSwyNS41NCwxOS4yN0wxODUuNiwyMDMuMkE4LDgsMCwwLDAsMTkyLDIxNmg0OGE4LDgsMCwwLDAsMC0xNloiLz48L3N2Zz4=)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNDgsNTZWMTc2YTQsNCwwLDAsMS04LDBWMTIwSDQ0djU2YTQsNCwwLDAsMS04LDBWNTZhNCw0LDAsMCwxLDgsMHY1Nmg5NlY1NmE0LDQsMCwwLDEsOCwwWm05MiwxNDhIMjAwbDM4LjM2LTUxLjE1YTI4LDI4LDAsMSwwLTQ4Ljc3LTI2LjE4LDQsNCwwLDEsMCw3LjU0LDIuNjZBMjAuMzYsMjAuMzYsMCwwLDEsMjAwLDEyNCwyMCwyMCwwLDAsMSwyMzIsMTQ4TDE4OC44LDIwNS42QTQsNCwwLDAsMCwxOTIsMjEyaDQ4YTQsNCwwLDAsMCwwLThaIi8+PC9zdmc+)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNTAsNTZWMTc2YTYsNiwwLDAsMS0xMiwwVjEyMkg0NnY1NGE2LDYsMCwwLDEtMTIsMFY1NmE2LDYsMCwwLDEsMTIsMHY1NGg5MlY1NmE2LDYsMCwwLDEsMTIsMFptOTAsMTQ2SDIwNEwyNDAsMTU0LjA1QTMwLDMwLDAsMSwwLDE4Ny43MSwxMjYsNiw2LDAsMSwwLDE5OSwxMzBhMTgsMTgsMCwwLDEsMTQuNDctMTEuODIsMTgsMTgsMCwwLDEsMTYuODcsMjguNjZMMTg3LjIsMjA0LjRBNiw2LDAsMCwwLDE5MiwyMTRoNDhhNiw2LDAsMCwwLDAtMTJaIi8+PC9zdmc+)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0xNTYsNTZWMTc2YTEyLDEyLDAsMCwxLTI0LDBWMTI4SDUydjQ4YTEyLDEyLDAsMCwxLTI0LDBWNTZhMTIsMTIsMCwwLDEsMjQsMHY0OGg4MFY1NmExMiwxMiwwLDAsMSwyNCwwWm04NCwxNDBIMjE2bDI4Ljc0LTM4LjMzQTM2LDM2LDAsMSwwLDE4Mi4wNSwxMjRhMTIsMTIsMCwwLDAsMjIuNjMsOCwxMS42NywxMS42NywwLDAsMSwxLjczLTMuMjIsMTIsMTIsMCwxLDEsMTkuMTUsMTQuNDZMMTgyLjQsMjAwLjhBMTIsMTIsMCwwLDAsMTkyLDIyMGg0OGExMiwxMiwwLDAsMCwwLTI0WiIvPjwvc3ZnPg==)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMDgsMzJINDhBMTYsMTYsMCwwLDAsMzIsNDhWMjA4YTE2LDE2LDAsMCwwLDE2LDE2SDIwOGExNiwxNiwwLDAsMCwxNi0xNlY0OEExNiwxNiwwLDAsMCwyMDgsMzJaTTEyOCwxNjBhOCw4LDAsMCwxLTE2LDBWMTI4SDcydjMyYTgsOCwwLDAsMS0xNiwwVjgwYTgsOCwwLDAsMSwxNiwwdjMyaDQwVjgwYTgsOCwwLDAsMSwxNiwwWm02NCwyNEgxNTJhOCw4LDAsMCwxLTYuNC0xMi44bDM2LTQ4YTEyLDEyLDAsMSwwLTE5LjE1LTE0LjQ2LDEzLjA2LDEzLjA2LDAsMCwwLTIuNTgsNC44MSw4LDgsMCwxLDEtMTUuNjgtMy4xOCwyOC4xNywyOC4xNywwLDEsMSw1MC4yLDIyLjQ0TDE2OCwxNjhoMjRhOCw4LDAsMCwxLDAsMTZaIi8+PC9zdmc+)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yNDAsNzJWMjA4SDU2YTE2LDE2LDAsMCwxLTE2LTE2VjU2SDIyNEExNiwxNiwwLDAsMSwyNDAsNzJaIiBvcGFjaXR5PSIwLjIiLz48cGF0aCBkPSJNMjQ4LDIwOGE4LDgsMCwwLDEtOCw4SDE5MmE4LDgsMCwwLDEtNi40LTEyLjhsNDMuMTYtNTcuNTZhMTYsMTYsMCwxLDAtMjUuNTQtMTkuMjcsMTYuMjgsMTYuMjgsMCwwLDAtMi4zMiw0LjMsOCw4LDAsMSwxLTE1LjA4LTUuMzQsMzIsMzIsMCwxLDEsNTUuNzMsMjkuOTNMMjA4LDIwMGgzMkE4LDgsMCwwLDEsMjQ4LDIwOFpNMTQ0LDQ4YTgsOCwwLDAsMC04LDh2NTJINDhWNTZhOCw4LDAsMCwwLTE2LDBWMTc2YTgsOCwwLDAsMCwxNiwwVjEyNGg4OHY1MmE4LDgsMCwwLDAsMTYsMFY1NkE4LDgsMCwwLDAsMTQ0LDQ4WiIvPjwvc3ZnPg==)
 */
declare const I: Icon;
/** @deprecated Use TextHTwoIcon */
export declare const TextHTwo: Icon;
export { I as TextHTwoIcon };
