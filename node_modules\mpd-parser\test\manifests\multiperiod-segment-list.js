export const parsedManifest = {
  allowCache: true,
  discontinuityStarts: [],
  duration: 12,
  endList: true,
  mediaGroups: {
    'AUDIO': {},
    'CLOSED-CAPTIONS': {},
    'SUBTITLES': {},
    'VIDEO': {}
  },
  playlists: [
    {
      attributes: {
        'AUDIO': 'audio',
        'BANDWIDTH': 449000,
        'CODECS': 'avc1.420015',
        'FRAME-RATE': 23.976,
        'NAME': '482',
        'PROGRAM-ID': 1,
        'RESOLUTION': {
          height: 270,
          width: 482
        },
        'SUBTITLES': 'subs'
      },
      endList: true,
      mediaSequence: 0,
      discontinuitySequence: 0,
      discontinuityStarts: [2],
      timelineStarts: [{
        start: 0,
        timeline: 0
      }, {
        start: 6,
        timeline: 6
      }],
      targetDuration: 3,
      resolvedUri: 'https://www.example.com/base',
      segments: [
        {
          duration: 3,
          map: {
            uri: '',
            resolvedUri: 'https://www.example.com/base'
          },
          resolvedUri: 'https://www.example.com/low/segment-1.ts',
          timeline: 0,
          presentationTime: 0,
          uri: 'low/segment-1.ts',
          number: 0
        },
        {
          duration: 3,
          map: {
            uri: '',
            resolvedUri: 'https://www.example.com/base'
          },
          resolvedUri: 'https://www.example.com/low/segment-2.ts',
          timeline: 0,
          presentationTime: 3,
          uri: 'low/segment-2.ts',
          number: 1
        },
        {
          discontinuity: true,
          duration: 3,
          map: {
            uri: '',
            resolvedUri: 'https://www.example.com/base'
          },
          resolvedUri: 'https://www.example.com/low/segment-1.ts',
          timeline: 6,
          presentationTime: 6,
          uri: 'low/segment-1.ts',
          number: 2
        },
        {
          duration: 3,
          map: {
            uri: '',
            resolvedUri: 'https://www.example.com/base'
          },
          resolvedUri: 'https://www.example.com/low/segment-2.ts',
          timeline: 6,
          presentationTime: 9,
          uri: 'low/segment-2.ts',
          number: 3
        }
      ],
      timeline: 0,
      uri: ''
    }
  ],
  segments: [],
  timelineStarts: [{
    start: 0,
    timeline: 0
  }, {
    start: 6,
    timeline: 6
  }],
  uri: ''
};
