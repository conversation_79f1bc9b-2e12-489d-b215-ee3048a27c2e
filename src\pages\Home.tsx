
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import TopBar from '../components/TopBar';
import FeaturedClip from '../components/FeaturedClip';
import ClipRow from '../components/ClipRow';
import NetflixLikePlayer from '../components/NetflixLikePlayer';
import SimpleVideoPlayer from '../components/SimpleVideoPlayer';
import { zimComedyVideos, getVideosByCategory } from '../data/zimComedyVideos';

interface Clip {
  id: string;
  title: string;
  thumbnail: string;
  author: string;
  likes: number;
  comments: number;
  views: string;
  duration: string;
  category: string;
  description?: string;
  videoUrl?: string;
}

const Home = () => {
  // Use the most popular video as featured
  const [featuredClip] = useState(zimComedyVideos[3]); // Zimbabwe's Got Talent - highest views

  const categoryVideos = getVideosByCategory();

  const [clipCategories] = useState([
    {
      title: 'Trending Now in Zimbabwe 🔥',
      clips: categoryVideos.trending
    },
    {
      title: 'Comedy Classics 🎭',
      clips: categoryVideos.classic
    },
    {
      title: 'Latest Uploads ⚡',
      clips: categoryVideos.latest
    },
    {
      title: 'All ZimFlix Originals 🇿🇼',
      clips: zimComedyVideos
    }
  ]);

  const [currentVideo, setCurrentVideo] = useState<{
    videoUrl?: string;
    title: string;
  } | null>(null);

  const navigate = useNavigate();

  const handlePlayClip = (clip: Clip) => {
    console.log('Playing clip:', clip.title);
    setCurrentVideo({
      videoUrl: clip.videoUrl || `./stream vid/${clip.title}.mp4`,
      title: clip.title
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <TopBar />
      
      {/* Main Content */}
      <div className="pt-20 pb-32">
        {/* Featured Clip Hero Section */}
        <FeaturedClip 
          clip={featuredClip} 
          onPlay={() => handlePlayClip(featuredClip)} 
        />

        {/* Clip Rows */}
        <div className="space-y-8">
          {clipCategories.map((category, index) => (
            <ClipRow
              key={index}
              title={category.title}
              clips={category.clips}
              onPlayClip={handlePlayClip}
            />
          ))}
        </div>
      </div>

      {/* Simple Video Player Modal */}
      {currentVideo && (
        <SimpleVideoPlayer
          videoUrl={currentVideo.videoUrl}
          title={currentVideo.title}
          description={currentVideo.description}
          onClose={() => setCurrentVideo(null)}
          autoplay={true}
        />
      )}
    </div>
  );
};

export default Home;
