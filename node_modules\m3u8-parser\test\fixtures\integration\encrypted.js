module.exports = {
  allowCache: true,
  dateRanges: [],
  iFramePlaylists: [],
  mediaSequence: 7794,
  discontinuitySequence: 0,
  discontinuityStarts: [],
  segments: [
    {
      duration: 2.833,
      timeline: 0,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      uri: 'http://media.example.com/fileSequence52-A.ts'
    },
    {
      duration: 15,
      timeline: 0,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      uri: 'http://media.example.com/fileSequence52-B.ts'
    },
    {
      duration: 13.333,
      timeline: 0,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=52'
      },
      uri: 'http://media.example.com/fileSequence52-C.ts'
    },
    {
      duration: 15,
      timeline: 0,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=53'
      },
      uri: 'http://media.example.com/fileSequence53-A.ts'
    },
    {
      duration: 14,
      timeline: 0,
      key: {
        method: 'AES-128',
        uri: 'https://priv.example.com/key.php?r=54',
        iv: new Uint32Array([0, 0, 331, 3063767524])
      },
      uri: 'http://media.example.com/fileSequence53-B.ts'
    },
    {
      duration: 15,
      timeline: 0,
      uri: 'http://media.example.com/fileSequence53-B.ts'
    }
  ],
  targetDuration: 15,
  version: 3
};
