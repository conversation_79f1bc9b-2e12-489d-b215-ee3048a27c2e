import { Icon } from '../lib/types';
/**
 * @regular ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMjQsMTI4YTgsOCwwLDAsMS04LDhIMTA0YTgsOCwwLDAsMSwwLTE2SDIxNkE4LDgsMCwwLDEsMjI0LDEyOFpNMTA0LDcySDIxNmE4LDgsMCwwLDAsMC0xNkgxMDRhOCw4LDAsMCwwLDAsMTZaTTIxNiwxODRIMTA0YTgsOCwwLDAsMCwwLDE2SDIxNmE4LDgsMCwwLDAsMC0xNlpNNDMuNTgsNTUuMTYsNDgsNTIuOTRWMTA0YTgsOCwwLDAsMCwxNiwwVjQwYTgsOCwwLDAsMC0xMS41OC03LjE2bC0xNiw4YTgsOCwwLDAsMCw3LjE2LDE0LjMyWk03OS43NywxNTYuNzJhMjMuNzMsMjMuNzMsMCwwLDAtOS42LTE1Ljk1LDI0Ljg2LDI0Ljg2LDAsMCwwLTM0LjExLDQuNywyMy42MywyMy42MywwLDAsMC0zLjU3LDYuNDYsOCw4LDAsMSwwLDE1LDUuNDcsNy44NCw3Ljg0LDAsMCwxLDEuMTgtMi4xMyw4Ljc2LDguNzYsMCwwLDEsMTItMS41OUE3LjkxLDcuOTEsMCwwLDEsNjMuOTMsMTU5YTcuNjQsNy42NCwwLDAsMS0xLjU3LDUuNzgsMSwxLDAsMCwwLS4wOC4xMUwzMy41OSwyMDMuMjFBOCw4LDAsMCwwLDQwLDIxNkg3MmE4LDgsMCwwLDAsMC0xNkg1NmwxOS4wOC0yNS41M0EyMy40NywyMy40NywwLDAsMCw3OS43NywxNTYuNzJaIi8+PC9zdmc+)
 * @thin ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMjAsMTI4YTQsNCwwLDAsMS00LDRIMTA0YTQsNCwwLDAsMSwwLThIMjE2QTQsNCwwLDAsMSwyMjAsMTI4Wk0xMDQsNjhIMjE2YTQsNCwwLDAsMCwwLThIMTA0YTQsNCwwLDAsMCwwLDhaTTIxNiwxODhIMTA0YTQsNCwwLDAsMCwwLDhIMjE2YTQsNCwwLDAsMCwwLThaTTQxLjc5LDUxLjU4LDUyLDQ2LjQ3VjEwNGE0LDQsMCwwLDAsOCwwVjQwYTQsNCwwLDAsMC01Ljc5LTMuNThsLTE2LDhhNCw0LDAsMSwwLDMuNTgsNy4xNlpNNzIsMjA0SDQ4bDIzLjg1LTMxLjkyYTE5LjU0LDE5LjU0LDAsMCwwLDQtMTQuOCwxOS43NiwxOS43NiwwLDAsMC04LTEzLjI4LDIwLjg0LDIwLjg0LDAsMCwwLTI4LjU5LDMuOTIsMTkuODUsMTkuODUsMCwwLDAtMyw1LjM4QTQsNCwwLDAsMCw0My43NiwxNTZhMTIuMSwxMi4xLDAsMCwxLDEuNzgtMy4yMiwxMi43OCwxMi43OCwwLDAsMSwxNy41NC0yLjM3LDExLjg1LDExLjg1LDAsMCwxLDQuODEsNy45NCwxMS42NSwxMS42NSwwLDAsMS0yLjQxLDguODVMMzYuOCwyMDUuNjFBNCw0LDAsMCwwLDQwLDIxMkg3MmE0LDQsMCwwLDAsMC04WiIvPjwvc3ZnPg==)
 * @light ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMjIsMTI4YTYsNiwwLDAsMS02LDZIMTA0YTYsNiwwLDAsMSwwLTEySDIxNkE2LDYsMCwwLDEsMjIyLDEyOFpNMTA0LDcwSDIxNmE2LDYsMCwwLDAsMC0xMkgxMDRhNiw2LDAsMCwwLDAsMTJaTTIxNiwxODZIMTA0YTYsNiwwLDAsMCwwLDEySDIxNmE2LDYsMCwwLDAsMC0xMlpNNDIuNjgsNTMuMzcsNTAsNDkuNzFWMTA0YTYsNiwwLDAsMCwxMiwwVjQwYTYsNiwwLDAsMC04LjY4LTUuMzdsLTE2LDhhNiw2LDAsMCwwLDUuMzYsMTAuNzRaTTcyLDIwMkg1MmwyMS40OC0yOC43NEEyMS41LDIxLjUsMCwwLDAsNzcuNzksMTU3LDIxLjc1LDIxLjc1LDAsMCwwLDY5LDE0Mi4zOGEyMi44NiwyMi44NiwwLDAsMC0zMS4zNSw0LjMxLDIyLjE4LDIyLjE4LDAsMCwwLTMuMjgsNS45Miw2LDYsMCwwLDAsMTEuMjgsNC4xMSw5Ljg3LDkuODcsMCwwLDEsMS40OC0yLjY3LDEwLjc4LDEwLjc4LDAsMCwxLDE0Ljc4LTIsOS44OSw5Ljg5LDAsMCwxLDQsNi42MSw5LjY0LDkuNjQsMCwwLDEtMiw3LjI4bC0uMDYuMDlMMzUuMiwyMDQuNDFBNiw2LDAsMCwwLDQwLDIxNEg3MmE2LDYsMCwwLDAsMC0xMloiLz48L3N2Zz4=)
 * @bold ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMjgsMTI4YTEyLDEyLDAsMCwxLTEyLDEySDExNmExMiwxMiwwLDAsMSwwLTI0SDIxNkExMiwxMiwwLDAsMSwyMjgsMTI4Wk0xMTYsNzZIMjE2YTEyLDEyLDAsMCwwLDAtMjRIMTE2YTEyLDEyLDAsMCwwLDAsMjRaTTIxNiwxODBIMTE2YTEyLDEyLDAsMCwwLDAsMjRIMjE2YTEyLDEyLDAsMCwwLDAtMjRaTTQ0LDU5LjMxVjEwNGExMiwxMiwwLDAsMCwyNCwwVjQwQTEyLDEyLDAsMCwwLDUwLjY0LDI5LjI3bC0xNiw4YTEyLDEyLDAsMCwwLDkuMzYsMjJabTM5LjczLDk2Ljg2YTI3LjcsMjcuNywwLDAsMC0xMS4yLTE4LjYzQTI4Ljg5LDI4Ljg5LDAsMCwwLDMyLjksMTQzYTI3LjcxLDI3LjcxLDAsMCwwLTQuMTcsNy41NCwxMiwxMiwwLDAsMCwyMi41NSw4LjIxLDQsNCwwLDAsMSwuNTgtMSw0Ljc4LDQuNzgsMCwwLDEsNi41LS44MiwzLjgyLDMuODIsMCwwLDEsMS42MSwyLjYsMy42MywzLjYzLDAsMCwxLS43NywyLjc3bC0uMTMuMTdMMzAuMzksMjAwLjgyQTEyLDEyLDAsMCwwLDQwLDIyMEg3MmExMiwxMiwwLDAsMCwwLTI0SDY0bDE0LjI4LTE5LjExQTI3LjQ4LDI3LjQ4LDAsMCwwLDgzLjczLDE1Ni4xN1oiLz48L3N2Zz4=)
 * @fill ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMDgsMzJINDhBMTYsMTYsMCwwLDAsMzIsNDhWMjA4YTE2LDE2LDAsMCwwLDE2LDE2SDIwOGExNiwxNiwwLDAsMCwxNi0xNlY0OEExNiwxNiwwLDAsMCwyMDgsMzJaTTU2Ljg0LDc1LjU4YTgsOCwwLDAsMSwzLjU4LTEwLjc0bDE2LThBOCw4LDAsMCwxLDg4LDY0djQ4YTgsOCwwLDAsMS0xNiwwVjc2Ljk0bC00LjQyLDIuMjJBOCw4LDAsMCwxLDU2Ljg0LDc1LjU4Wk05MiwxODBhOCw4LDAsMCwxLDAsMTZINjhhOCw4LDAsMCwxLTYuNC0xMi44bDIxLjY3LTI4Ljg5QTMuOTIsMy45MiwwLDAsMCw4NCwxNTJhNCw0LDAsMCwwLTcuNzctMS4zMyw4LDgsMCwwLDEtMTUuMDktNS4zNCwyMCwyMCwwLDEsMSwzNSwxOC41M0w4NCwxODBabTEwMCw0SDEyMGE4LDgsMCwwLDEsMC0xNmg3MmE4LDgsMCwwLDEsMCwxNlptMC00OEgxMjBhOCw4LDAsMCwxLDAtMTZoNzJhOCw4LDAsMCwxLDAsMTZabTAtNDhIMTIwYTgsOCwwLDAsMSwwLTE2aDcyYTgsOCwwLDAsMSwwLDE2WiIvPjwvc3ZnPg==)
 * @duotone ![img](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI1NiAyNTYiIGZpbGw9IiMwMDAiPjxyZWN0IHdpZHRoPSIyNTYiIGhlaWdodD0iMjU2IiBmaWxsPSIjRkZGIiByeD0iNDAiIHJ5PSI0MCIvPjxwYXRoIGQ9Ik0yMTYsNjRWMTkySDEwNFY2NFoiIG9wYWNpdHk9IjAuMiIvPjxwYXRoIGQ9Ik0yMjQsMTI4YTgsOCwwLDAsMS04LDhIMTA0YTgsOCwwLDAsMSwwLTE2SDIxNkE4LDgsMCwwLDEsMjI0LDEyOFpNMTA0LDcySDIxNmE4LDgsMCwwLDAsMC0xNkgxMDRhOCw4LDAsMCwwLDAsMTZaTTIxNiwxODRIMTA0YTgsOCwwLDAsMCwwLDE2SDIxNmE4LDgsMCwwLDAsMC0xNlpNNDMuNTgsNTUuMTYsNDgsNTIuOTRWMTA0YTgsOCwwLDAsMCwxNiwwVjQwYTgsOCwwLDAsMC0xMS41OC03LjE2bC0xNiw4YTgsOCwwLDAsMCw3LjE2LDE0LjMyWk03OS43NywxNTYuNzJhMjMuNzMsMjMuNzMsMCwwLDAtOS42LTE1Ljk1LDI0Ljg2LDI0Ljg2LDAsMCwwLTM0LjExLDQuNywyMy42MywyMy42MywwLDAsMC0zLjU3LDYuNDYsOCw4LDAsMSwwLDE1LDUuNDcsNy44NCw3Ljg0LDAsMCwxLDEuMTgtMi4xMyw4Ljc2LDguNzYsMCwwLDEsMTItMS41OUE3LjkxLDcuOTEsMCwwLDEsNjMuOTMsMTU5YTcuNjQsNy42NCwwLDAsMS0xLjU3LDUuNzgsMSwxLDAsMCwwLS4wOC4xMUwzMy41OSwyMDMuMjFBOCw4LDAsMCwwLDQwLDIxNkg3MmE4LDgsMCwwLDAsMC0xNkg1NmwxOS4wOC0yNS41M0EyMy40NywyMy40NywwLDAsMCw3OS43NywxNTYuNzJaIi8+PC9zdmc+)
 */
declare const I: Icon;
/** @deprecated Use ListNumbersIcon */
export declare const ListNumbers: Icon;
export { I as ListNumbersIcon };
