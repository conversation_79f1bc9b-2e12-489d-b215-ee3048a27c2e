export const parsedManifest = {
  allowCache: true,
  discontinuityStarts: [],
  segments: [],
  timelineStarts: [{ start: 0, timeline: 0 }],
  endList: true,
  mediaGroups: {
    'AUDIO': {
      audio: {
        'en (main)': {
          language: 'en',
          autoselect: true,
          default: true,
          playlists: [
            {
              attributes: {
                'NAME': '63000',
                'BANDWIDTH': 63000,
                'CODECS': 'mp4a.40.2',
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://www.example.com/base',
              targetDuration: 1.984,
              segments: [
                {
                  uri: '63000/0.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/0.m4f',
                  map: {
                    uri: '63000/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/init.m4f'
                  },
                  number: 0,
                  presentationTime: 0
                },
                {
                  uri: '63000/1.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/1.m4f',
                  map: {
                    uri: '63000/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/init.m4f'
                  },
                  number: 1,
                  presentationTime: 1.984
                },
                {
                  uri: '63000/2.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/2.m4f',
                  map: {
                    uri: '63000/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/init.m4f'
                  },
                  number: 2,
                  presentationTime: 3.968
                },
                {
                  uri: '63000/3.m4f',
                  timeline: 0,
                  duration: 0.04800000000000004,
                  resolvedUri: 'https://www.example.com/63000/3.m4f',
                  map: {
                    uri: '63000/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/init.m4f'
                  },
                  number: 3,
                  presentationTime: 5.952
                }
              ],
              mediaSequence: 0,
              contentProtection: {
                'com.widevine.alpha': {
                  attributes: {
                    schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
                  },
                  pssh: new Uint8Array([181, 235, 45])
                },
                'mp4protection': {
                  attributes: {
                    'cenc:default_KID': 'aaa',
                    'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
                    'value': 'cenc'
                  }
                }
              }
            },
            {
              attributes: {
                'NAME': '125000',
                'BANDWIDTH': 125000,
                'CODECS': 'mp4a.40.2',
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://www.example.com/base',
              targetDuration: 1.984,
              segments: [
                {
                  uri: '125000/0.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/0.m4f',
                  map: {
                    uri: '125000/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/init.m4f'
                  },
                  number: 0,
                  presentationTime: 0
                },
                {
                  uri: '125000/1.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/1.m4f',
                  map: {
                    uri: '125000/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/init.m4f'
                  },
                  number: 1,
                  presentationTime: 1.984
                },
                {
                  uri: '125000/2.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/2.m4f',
                  map: {
                    uri: '125000/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/init.m4f'
                  },
                  number: 2,
                  presentationTime: 3.968
                },
                {
                  uri: '125000/3.m4f',
                  timeline: 0,
                  duration: 0.04800000000000004,
                  resolvedUri: 'https://www.example.com/125000/3.m4f',
                  map: {
                    uri: '125000/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/init.m4f'
                  },
                  number: 3,
                  presentationTime: 5.952
                }
              ],
              mediaSequence: 0,
              contentProtection: {
                'com.widevine.alpha': {
                  attributes: {
                    schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
                  },
                  pssh: new Uint8Array([181, 235, 45])
                },
                'mp4protection': {
                  attributes: {
                    'cenc:default_KID': 'aaa',
                    'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
                    'value': 'cenc'
                  }
                }
              }
            }
          ],
          uri: ''
        },
        'es': {
          language: 'es',
          autoselect: true,
          default: false,
          playlists: [
            {
              attributes: {
                'NAME': '63000',
                'BANDWIDTH': 63000,
                'CODECS': 'mp4a.40.2',
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://www.example.com/base',
              targetDuration: 1.984,
              segments: [
                {
                  uri: '63000/es/0.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/es/0.m4f',
                  map: {
                    uri: '63000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/es/init.m4f'
                  },
                  number: 0,
                  presentationTime: 0
                },
                {
                  uri: '63000/es/1.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/es/1.m4f',
                  map: {
                    uri: '63000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/es/init.m4f'
                  },
                  number: 1,
                  presentationTime: 1.984
                },
                {
                  uri: '63000/es/2.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/63000/es/2.m4f',
                  map: {
                    uri: '63000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/es/init.m4f'
                  },
                  number: 2,
                  presentationTime: 3.968
                },
                {
                  uri: '63000/es/3.m4f',
                  timeline: 0,
                  duration: 0.04800000000000004,
                  resolvedUri: 'https://www.example.com/63000/es/3.m4f',
                  map: {
                    uri: '63000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/63000/es/init.m4f'
                  },
                  number: 3,
                  presentationTime: 5.952
                }
              ],
              mediaSequence: 0,
              contentProtection: {
                'com.widevine.alpha': {
                  attributes: {
                    schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
                  },
                  pssh: new Uint8Array([181, 235, 45])
                },
                'mp4protection': {
                  attributes: {
                    'cenc:default_KID': 'aaa',
                    'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
                    'value': 'cenc'
                  }
                }
              }
            },
            {
              attributes: {
                'NAME': '125000',
                'BANDWIDTH': 125000,
                'CODECS': 'mp4a.40.2',
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://www.example.com/base',
              targetDuration: 1.984,
              segments: [
                {
                  uri: '125000/es/0.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/es/0.m4f',
                  map: {
                    uri: '125000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/es/init.m4f'
                  },
                  number: 0,
                  presentationTime: 0
                },
                {
                  uri: '125000/es/1.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/es/1.m4f',
                  map: {
                    uri: '125000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/es/init.m4f'
                  },
                  number: 1,
                  presentationTime: 1.984
                },
                {
                  uri: '125000/es/2.m4f',
                  timeline: 0,
                  duration: 1.984,
                  resolvedUri: 'https://www.example.com/125000/es/2.m4f',
                  map: {
                    uri: '125000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/es/init.m4f'
                  },
                  number: 2,
                  presentationTime: 3.968
                },
                {
                  uri: '125000/es/3.m4f',
                  timeline: 0,
                  duration: 0.04800000000000004,
                  resolvedUri: 'https://www.example.com/125000/es/3.m4f',
                  map: {
                    uri: '125000/es/init.m4f',
                    resolvedUri: 'https://www.example.com/125000/es/init.m4f'
                  },
                  number: 3,
                  presentationTime: 5.952
                }
              ],
              mediaSequence: 0,
              contentProtection: {
                'com.widevine.alpha': {
                  attributes: {
                    schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
                  },
                  pssh: new Uint8Array([181, 235, 45])
                },
                'mp4protection': {
                  attributes: {
                    'cenc:default_KID': 'aaa',
                    'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
                    'value': 'cenc'
                  }
                }
              }
            }
          ],
          uri: ''
        }
      }
    },
    'VIDEO': {},
    'CLOSED-CAPTIONS': {},
    'SUBTITLES': {
      subs: {
        en: {
          language: 'en',
          default: false,
          autoselect: false,
          playlists: [
            {
              attributes: {
                'NAME': 'en',
                'BANDWIDTH': 256,
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://example.com/en.vtt',
              targetDuration: 6,
              segments: [
                {
                  uri: 'https://example.com/en.vtt',
                  timeline: 0,
                  resolvedUri: 'https://example.com/en.vtt',
                  duration: 6,
                  number: 0
                }
              ],
              mediaSequence: 0
            }
          ],
          uri: ''
        },
        es: {
          language: 'es',
          default: false,
          autoselect: false,
          playlists: [
            {
              attributes: {
                'NAME': 'es',
                'BANDWIDTH': 256,
                'PROGRAM-ID': 1
              },
              uri: '',
              endList: true,
              timeline: 0,
              timelineStarts: [{ start: 0, timeline: 0 }],
              discontinuitySequence: 0,
              discontinuityStarts: [],
              resolvedUri: 'https://example.com/es.vtt',
              targetDuration: 6,
              segments: [
                {
                  uri: 'https://example.com/es.vtt',
                  timeline: 0,
                  resolvedUri: 'https://example.com/es.vtt',
                  duration: 6,
                  number: 0
                }
              ],
              mediaSequence: 0
            }
          ],
          uri: ''
        }
      }
    }
  },
  uri: '',
  duration: 6,
  playlists: [
    {
      attributes: {
        'NAME': '482',
        'AUDIO': 'audio',
        'SUBTITLES': 'subs',
        'FRAME-RATE': 23.976,
        'RESOLUTION': {
          width: 482,
          height: 270
        },
        'CODECS': 'avc1.420015',
        'BANDWIDTH': 449000,
        'PROGRAM-ID': 1
      },
      uri: '',
      endList: true,
      timeline: 0,
      timelineStarts: [{ start: 0, timeline: 0 }],
      discontinuitySequence: 0,
      discontinuityStarts: [],
      resolvedUri: 'https://www.example.com/base',
      targetDuration: 1.9185833333333333,
      segments: [
        {
          uri: '482/0.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/482/0.m4f',
          map: {
            uri: '482/init.m4f',
            resolvedUri: 'https://www.example.com/482/init.m4f'
          },
          number: 0,
          presentationTime: 0
        },
        {
          uri: '482/1.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/482/1.m4f',
          map: {
            uri: '482/init.m4f',
            resolvedUri: 'https://www.example.com/482/init.m4f'
          },
          number: 1,
          presentationTime: 1.9185833333333333
        },
        {
          uri: '482/2.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/482/2.m4f',
          map: {
            uri: '482/init.m4f',
            resolvedUri: 'https://www.example.com/482/init.m4f'
          },
          number: 2,
          presentationTime: 3.8371666666666666
        },
        {
          uri: '482/3.m4f',
          timeline: 0,
          duration: 0.24425000000000008,
          resolvedUri: 'https://www.example.com/482/3.m4f',
          map: {
            uri: '482/init.m4f',
            resolvedUri: 'https://www.example.com/482/init.m4f'
          },
          number: 3,
          presentationTime: 5.75575
        }
      ],
      mediaSequence: 0,
      contentProtection: {
        'com.widevine.alpha': {
          attributes: {
            schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
          },
          pssh: new Uint8Array([181, 235, 45])
        },
        'mp4protection': {
          attributes: {
            'cenc:default_KID': 'aaa',
            'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
            'value': 'cenc'
          }
        }
      }
    },
    {
      attributes: {
        'NAME': '720',
        'AUDIO': 'audio',
        'SUBTITLES': 'subs',
        'FRAME-RATE': 23.976,
        'RESOLUTION': {
          width: 720,
          height: 404
        },
        'CODECS': 'avc1.64001e',
        'BANDWIDTH': 3971000,
        'PROGRAM-ID': 1
      },
      uri: '',
      endList: true,
      timeline: 0,
      timelineStarts: [{ start: 0, timeline: 0 }],
      discontinuitySequence: 0,
      discontinuityStarts: [],
      resolvedUri: 'https://www.example.com/base',
      targetDuration: 1.9185833333333333,
      segments: [
        {
          uri: '720/0.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/720/0.m4f',
          map: {
            uri: '720/init.m4f',
            resolvedUri: 'https://www.example.com/720/init.m4f'
          },
          number: 0,
          presentationTime: 0
        },
        {
          uri: '720/1.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/720/1.m4f',
          map: {
            uri: '720/init.m4f',
            resolvedUri: 'https://www.example.com/720/init.m4f'
          },
          number: 1,
          presentationTime: 1.9185833333333333
        },
        {
          uri: '720/2.m4f',
          timeline: 0,
          duration: 1.9185833333333333,
          resolvedUri: 'https://www.example.com/720/2.m4f',
          map: {
            uri: '720/init.m4f',
            resolvedUri: 'https://www.example.com/720/init.m4f'
          },
          number: 2,
          presentationTime: 3.8371666666666666
        },
        {
          uri: '720/3.m4f',
          timeline: 0,
          duration: 0.24425000000000008,
          resolvedUri: 'https://www.example.com/720/3.m4f',
          map: {
            uri: '720/init.m4f',
            resolvedUri: 'https://www.example.com/720/init.m4f'
          },
          number: 3,
          presentationTime: 5.75575
        }
      ],
      mediaSequence: 0,
      contentProtection: {
        'com.widevine.alpha': {
          attributes: {
            schemeIdUri: 'urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed'
          },
          pssh: new Uint8Array([181, 235, 45])
        },
        'mp4protection': {
          attributes: {
            'cenc:default_KID': 'aaa',
            'schemeIdUri': 'urn:mpeg:dash:mp4protection:2011',
            'value': 'cenc'
          }
        }
      }
    }
  ]
};
