/*! @name mpd-parser @version 1.3.1 @license Apache-2.0 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@xmldom/xmldom")):"function"==typeof define&&define.amd?define(["exports","@xmldom/xmldom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).mpdParser={},e.window)}(this,(function(e,t){"use strict";const n=e=>!!e&&"object"==typeof e,i=(...e)=>e.reduce(((e,t)=>("object"!=typeof t||Object.keys(t).forEach((r=>{Array.isArray(e[r])&&Array.isArray(t[r])?e[r]=e[r].concat(t[r]):n(e[r])&&n(t[r])?e[r]=i(e[r],t[r]):e[r]=t[r]})),e)),{}),r=e=>Object.keys(e).map((t=>e[t])),a=e=>e.reduce(((e,t)=>e.concat(t)),[]),s=e=>{if(!e.length)return[];const t=[];for(let n=0;n<e.length;n++)t.push(e[n]);return t};var o="INVALID_NUMBER_OF_PERIOD",u="DASH_EMPTY_MANIFEST",c="DASH_INVALID_XML",l="NO_BASE_URL",d="SEGMENT_TIME_UNSPECIFIED",m="UNSUPPORTED_UTC_TIMING_SCHEME",p={exports:{}};!function(e,t){!function(t){var n=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,i=/^(?=([^\/?#]*))\1([^]*)$/,r=/(?:\/|^)\.(?=\/)/g,a=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,s={buildAbsoluteURL:function(e,t,n){if(n=n||{},e=e.trim(),!(t=t.trim())){if(!n.alwaysNormalize)return e;var r=s.parseURL(e);if(!r)throw new Error("Error trying to parse base URL.");return r.path=s.normalizePath(r.path),s.buildURLFromParts(r)}var a=s.parseURL(t);if(!a)throw new Error("Error trying to parse relative URL.");if(a.scheme)return n.alwaysNormalize?(a.path=s.normalizePath(a.path),s.buildURLFromParts(a)):t;var o=s.parseURL(e);if(!o)throw new Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var u=i.exec(o.path);o.netLoc=u[1],o.path=u[2]}o.netLoc&&!o.path&&(o.path="/");var c={scheme:o.scheme,netLoc:a.netLoc,path:null,params:a.params,query:a.query,fragment:a.fragment};if(!a.netLoc&&(c.netLoc=o.netLoc,"/"!==a.path[0]))if(a.path){var l=o.path,d=l.substring(0,l.lastIndexOf("/")+1)+a.path;c.path=s.normalizePath(d)}else c.path=o.path,a.params||(c.params=o.params,a.query||(c.query=o.query));return null===c.path&&(c.path=n.alwaysNormalize?s.normalizePath(a.path):a.path),s.buildURLFromParts(c)},parseURL:function(e){var t=n.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(r,"");e.length!==(e=e.replace(a,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}};e.exports=s}()}(p);var f=p.exports,g="http://example.com",h=function(e,t){if(/^[a-z]+:/i.test(t))return t;/^data:/.test(e)&&(e=window.location&&window.location.href||"");var n="function"==typeof window.URL,i=/^\/\//.test(e),r=!window.location&&!/\/\//i.test(e);if(n?e=new window.URL(e,window.location||g):/\/\//i.test(e)||(e=f.buildAbsoluteURL(window.location&&window.location.href||"",e)),n){var a=new URL(t,e);return r?a.href.slice(g.length):i?a.href.slice(a.protocol.length):a.href}return f.buildAbsoluteURL(e,t)};const b=({baseUrl:e="",source:t="",range:n="",indexRange:i=""})=>{const r={uri:t,resolvedUri:h(e||"",t)};if(n||i){const e=(n||i).split("-");let t,a=window.BigInt?window.BigInt(e[0]):parseInt(e[0],10),s=window.BigInt?window.BigInt(e[1]):parseInt(e[1],10);a<Number.MAX_SAFE_INTEGER&&"bigint"==typeof a&&(a=Number(a)),s<Number.MAX_SAFE_INTEGER&&"bigint"==typeof s&&(s=Number(s)),t="bigint"==typeof s||"bigint"==typeof a?window.BigInt(s)-window.BigInt(a)+window.BigInt(1):s-a+1,"bigint"==typeof t&&t<Number.MAX_SAFE_INTEGER&&(t=Number(t)),r.byterange={length:t,offset:a}}return r},y=e=>(e&&"number"!=typeof e&&(e=parseInt(e,10)),isNaN(e)?null:e),S={static(e){const{duration:t,timescale:n=1,sourceDuration:i,periodDuration:r}=e,a=y(e.endNumber),s=t/n;return"number"==typeof a?{start:0,end:a}:"number"==typeof r?{start:0,end:r/s}:{start:0,end:i/s}},dynamic(e){const{NOW:t,clientOffset:n,availabilityStartTime:i,timescale:r=1,duration:a,periodStart:s=0,minimumUpdatePeriod:o=0,timeShiftBufferDepth:u=1/0}=e,c=y(e.endNumber),l=(t+n)/1e3,d=i+s,m=l+o-d,p=Math.ceil(m*r/a),f=Math.floor((l-d-u)*r/a),g=Math.floor((l-d)*r/a);return{start:Math.max(0,f),end:"number"==typeof c?c:Math.min(p,g)}}},w=e=>{const{type:t,duration:n,timescale:i=1,periodDuration:r,sourceDuration:a}=e,{start:s,end:o}=S[t](e),u=((e,t)=>{const n=[];for(let i=e;i<t;i++)n.push(i);return n})(s,o).map((e=>t=>{const{duration:n,timescale:i=1,periodStart:r,startNumber:a=1}=e;return{number:a+t,duration:n/i,timeline:r,time:t*n}})(e));if("static"===t){const e=u.length-1,t="number"==typeof r?r:a;u[e].duration=t-n/i*e}return u},U=e=>{const{baseUrl:t,initialization:n={},sourceDuration:i,indexRange:r="",periodStart:a,presentationTime:s,number:o=0,duration:u}=e;if(!t)throw new Error(l);const c=b({baseUrl:t,source:n.sourceURL,range:n.range}),d=b({baseUrl:t,source:t,indexRange:r});if(d.map=c,u){const t=w(e);t.length&&(d.duration=t[0].duration,d.timeline=t[0].timeline)}else i&&(d.duration=i,d.timeline=a);return d.presentationTime=s||a,d.number=o,[d]},I=(e,t,n)=>{const i=e.sidx.map?e.sidx.map:null,r=e.sidx.duration,a=e.timeline||0,s=e.sidx.byterange,o=s.offset+s.length,u=t.timescale,c=t.references.filter((e=>1!==e.referenceType)),l=[],d=e.endList?"static":"dynamic",m=e.sidx.timeline;let p,f=m,g=e.mediaSequence||0;p="bigint"==typeof t.firstOffset?window.BigInt(o)+t.firstOffset:o+t.firstOffset;for(let e=0;e<c.length;e++){const s=t.references[e],o=s.referencedSize,c=s.subsegmentDuration;let h;h="bigint"==typeof p?p+window.BigInt(o)-window.BigInt(1):p+o-1;const b=U({baseUrl:n,timescale:u,timeline:a,periodStart:m,presentationTime:f,number:g,duration:c,sourceDuration:r,indexRange:`${p}-${h}`,type:d})[0];i&&(b.map=i),l.push(b),p+="bigint"==typeof p?window.BigInt(o):o,f+=c/u,g++}return e.segments=l,e};const v=["AUDIO","SUBTITLES"],E=e=>{return(t=e,n=({timeline:e})=>e,r(t.reduce(((e,t)=>(t.forEach((t=>{e[n(t)]=t})),e)),{}))).sort(((e,t)=>e.timeline>t.timeline?1:-1));var t,n},T=e=>{let t=[];var n,i;return n=e,i=(e,n,i,r)=>{t=t.concat(e.playlists||[])},v.forEach((function(e){for(var t in n.mediaGroups[e])for(var r in n.mediaGroups[e][t]){var a=n.mediaGroups[e][t][r];i(a,e,t,r)}})),t},L=({playlist:e,mediaSequence:t})=>{e.mediaSequence=t,e.segments.forEach(((t,n)=>{t.number=e.mediaSequence+n}))},D=({oldManifest:e,newManifest:t})=>{const n=e.playlists.concat(T(e)),i=t.playlists.concat(T(t));return t.timelineStarts=E([e.timelineStarts,t.timelineStarts]),(({oldPlaylists:e,newPlaylists:t,timelineStarts:n})=>{t.forEach((t=>{t.discontinuitySequence=n.findIndex((function({timeline:e}){return e===t.timeline}));const i=((e,t)=>{for(let n=0;n<e.length;n++)if(e[n].attributes.NAME===t)return e[n];return null})(e,t.attributes.NAME);if(!i)return;if(t.sidx)return;const r=t.segments[0],a=i.segments.findIndex((function(e){return Math.abs(e.presentationTime-r.presentationTime)<.016666666666666666}));if(-1===a)return L({playlist:t,mediaSequence:i.mediaSequence+i.segments.length}),t.segments[0].discontinuity=!0,t.discontinuityStarts.unshift(0),void((!i.segments.length&&t.timeline>i.timeline||i.segments.length&&t.timeline>i.segments[i.segments.length-1].timeline)&&t.discontinuitySequence--);i.segments[a].discontinuity&&!r.discontinuity&&(r.discontinuity=!0,t.discontinuityStarts.unshift(0),t.discontinuitySequence--),L({playlist:t,mediaSequence:i.segments[a].number})}))})({oldPlaylists:n,newPlaylists:i,timelineStarts:t.timelineStarts}),t},R=e=>e&&e.uri+"-"+(e=>{let t;return t="bigint"==typeof e.offset||"bigint"==typeof e.length?window.BigInt(e.offset)+window.BigInt(e.length)-window.BigInt(1):e.offset+e.length-1,`${e.offset}-${t}`})(e.byterange),P=e=>{const t=e.reduce((function(e,t){return e[t.attributes.baseUrl]||(e[t.attributes.baseUrl]=[]),e[t.attributes.baseUrl].push(t),e}),{});let n=[];return Object.values(t).forEach((e=>{const t=r(e.reduce(((e,t)=>{const n=t.attributes.id+(t.attributes.lang||"");return e[n]?(t.segments&&(t.segments[0]&&(t.segments[0].discontinuity=!0),e[n].segments.push(...t.segments)),t.attributes.contentProtection&&(e[n].attributes.contentProtection=t.attributes.contentProtection)):(e[n]=t,e[n].attributes.timelineStarts=[]),e[n].attributes.timelineStarts.push({start:t.attributes.periodStart,timeline:t.attributes.periodStart}),e}),{}));n=n.concat(t)})),n.map((e=>{var t,n;return e.discontinuityStarts=(t=e.segments||[],n="discontinuity",t.reduce(((e,t,i)=>(t[n]&&e.push(i),e)),[])),e}))},N=(e,t)=>{const n=R(e.sidx),i=n&&t[n]&&t[n].sidx;return i&&I(e,i,e.sidx.resolvedUri),e},O=(e,t={})=>{if(!Object.keys(t).length)return e;for(const n in e)e[n]=N(e[n],t);return e},x=({attributes:e,segments:t,sidx:n,discontinuityStarts:i})=>{const r={attributes:{NAME:e.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:e.width,height:e.height},CODECS:e.codecs,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuityStarts:i,timelineStarts:e.timelineStarts,segments:t};return e.frameRate&&(r.attributes["FRAME-RATE"]=e.frameRate),e.contentProtection&&(r.contentProtection=e.contentProtection),e.serviceLocation&&(r.attributes.serviceLocation=e.serviceLocation),n&&(r.sidx=n),r},A=({attributes:e})=>"video/mp4"===e.mimeType||"video/webm"===e.mimeType||"video"===e.contentType,M=({attributes:e})=>"audio/mp4"===e.mimeType||"audio/webm"===e.mimeType||"audio"===e.contentType,B=({attributes:e})=>"text/vtt"===e.mimeType||"text"===e.contentType,q=e=>e?Object.keys(e).reduce(((t,n)=>{const i=e[n];return t.concat(i.playlists)}),[]):[],C=({dashPlaylists:e,locations:t,contentSteering:n,sidxMapping:i={},previousManifest:r,eventStream:a})=>{if(!e.length)return{};const{sourceDuration:s,type:o,suggestedPresentationDelay:u,minimumUpdatePeriod:c}=e[0].attributes,l=P(e.filter(A)).map(x),d=P(e.filter(M)),m=P(e.filter(B)),p=e.map((e=>e.attributes.captionServices)).filter(Boolean),f={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:"",duration:s,playlists:O(l,i)};c>=0&&(f.minimumUpdatePeriod=1e3*c),t&&(f.locations=t),n&&(f.contentSteering=n),"dynamic"===o&&(f.suggestedPresentationDelay=u),a&&a.length>0&&(f.eventStream=a);const g=0===f.playlists.length,h=d.length?((e,t={},n=!1)=>{let i;const r=e.reduce(((e,r)=>{const a=r.attributes.role&&r.attributes.role.value||"",s=r.attributes.lang||"";let o=r.attributes.label||"main";if(s&&!r.attributes.label){const e=a?` (${a})`:"";o=`${r.attributes.lang}${e}`}e[o]||(e[o]={language:s,autoselect:!0,default:"main"===a,playlists:[],uri:""});const u=N((({attributes:e,segments:t,sidx:n,mediaSequence:i,discontinuitySequence:r,discontinuityStarts:a},s)=>{const o={attributes:{NAME:e.id,BANDWIDTH:e.bandwidth,CODECS:e.codecs,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuitySequence:r,discontinuityStarts:a,timelineStarts:e.timelineStarts,mediaSequence:i,segments:t};return e.contentProtection&&(o.contentProtection=e.contentProtection),e.serviceLocation&&(o.attributes.serviceLocation=e.serviceLocation),n&&(o.sidx=n),s&&(o.attributes.AUDIO="audio",o.attributes.SUBTITLES="subs"),o})(r,n),t);return e[o].playlists.push(u),void 0===i&&"main"===a&&(i=r,i.default=!0),e}),{});i||(r[Object.keys(r)[0]].default=!0);return r})(d,i,g):null,b=m.length?((e,t={})=>e.reduce(((e,n)=>{const i=n.attributes.label||n.attributes.lang||"text",r=n.attributes.lang||"und";return e[i]||(e[i]={language:r,default:!1,autoselect:!1,playlists:[],uri:""}),e[i].playlists.push(N((({attributes:e,segments:t,mediaSequence:n,discontinuityStarts:i,discontinuitySequence:r})=>{void 0===t&&(t=[{uri:e.baseUrl,timeline:e.periodStart,resolvedUri:e.baseUrl||"",duration:e.sourceDuration,number:0}],e.duration=e.sourceDuration);const a={NAME:e.id,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1};e.codecs&&(a.CODECS=e.codecs);const s={attributes:a,uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,timelineStarts:e.timelineStarts,discontinuityStarts:i,discontinuitySequence:r,mediaSequence:n,segments:t};return e.serviceLocation&&(s.attributes.serviceLocation=e.serviceLocation),s})(n),t)),e}),{}))(m,i):null,y=l.concat(q(h),q(b)),S=y.map((({timelineStarts:e})=>e));var w,U;return f.timelineStarts=E(S),w=y,U=f.timelineStarts,w.forEach((e=>{e.mediaSequence=0,e.discontinuitySequence=U.findIndex((function({timeline:t}){return t===e.timeline})),e.segments&&e.segments.forEach(((e,t)=>{e.number=t}))})),h&&(f.mediaGroups.AUDIO.audio=h),b&&(f.mediaGroups.SUBTITLES.subs=b),p.length&&(f.mediaGroups["CLOSED-CAPTIONS"].cc=p.reduce(((e,t)=>t?(t.forEach((t=>{const{channel:n,language:i}=t;e[i]={autoselect:!1,default:!1,instreamId:n,language:i},t.hasOwnProperty("aspectRatio")&&(e[i].aspectRatio=t.aspectRatio),t.hasOwnProperty("easyReader")&&(e[i].easyReader=t.easyReader),t.hasOwnProperty("3D")&&(e[i]["3D"]=t["3D"])})),e):e),{})),r?D({oldManifest:r,newManifest:f}):f},z=(e,t,n)=>{const{NOW:i,clientOffset:r,availabilityStartTime:a,timescale:s=1,periodStart:o=0,minimumUpdatePeriod:u=0}=e,c=(i+r)/1e3+u-(a+o);return Math.ceil((c*s-t)/n)},_=(e,t)=>{const{type:n,minimumUpdatePeriod:i=0,media:r="",sourceDuration:a,timescale:s=1,startNumber:o=1,periodStart:u}=e,c=[];let l=-1;for(let d=0;d<t.length;d++){const m=t[d],p=m.d,f=m.r||0,g=m.t||0;let h;if(l<0&&(l=g),g&&g>l&&(l=g),f<0){const o=d+1;h=o===t.length?"dynamic"===n&&i>0&&r.indexOf("$Number$")>0?z(e,l,p):(a*s-l)/p:(t[o].t-l)/p}else h=f+1;const b=o+c.length+h;let y=o+c.length;for(;y<b;)c.push({number:y,duration:p/s,time:l,timeline:u}),l+=p,y++}return c},F=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,$=(e,t)=>e.replace(F,(e=>(t,n,i,r)=>{if("$$"===t)return"$";if(void 0===e[n])return t;const a=""+e[n];return"RepresentationID"===n?a:(r=i?parseInt(r,10):1,a.length>=r?a:`${new Array(r-a.length+1).join("0")}${a}`)})(t)),G=(e,t)=>{const n={RepresentationID:e.id,Bandwidth:e.bandwidth||0},{initialization:i={sourceURL:"",range:""}}=e,r=b({baseUrl:e.baseUrl,source:$(i.sourceURL,n),range:i.range}),a=((e,t)=>e.duration||t?e.duration?w(e):_(e,t):[{number:e.startNumber||1,duration:e.sourceDuration,time:0,timeline:e.periodStart}])(e,t);return a.map((t=>{n.Number=t.number,n.Time=t.time;const i=$(e.media||"",n),a=e.timescale||1,s=e.presentationTimeOffset||0,o=e.periodStart+(t.time-s)/a;return{uri:i,timeline:t.timeline,duration:t.duration,resolvedUri:h(e.baseUrl||"",i),map:r,number:t.number,presentationTime:o}}))},j=(e,t)=>{const{duration:n,segmentUrls:i=[],periodStart:r}=e;if(!n&&!t||n&&t)throw new Error(d);const a=i.map((t=>((e,t)=>{const{baseUrl:n,initialization:i={}}=e,r=b({baseUrl:n,source:i.sourceURL,range:i.range}),a=b({baseUrl:n,source:t.media,range:t.mediaRange});return a.map=r,a})(e,t)));let s;n&&(s=w(e)),t&&(s=_(e,t));return s.map(((t,n)=>{if(a[n]){const i=a[n],s=e.timescale||1,o=e.presentationTimeOffset||0;return i.timeline=t.timeline,i.duration=t.duration,i.number=t.number,i.presentationTime=r+(t.time-o)/s,i}})).filter((e=>e))},k=({attributes:e,segmentInfo:t})=>{let n,r;t.template?(r=G,n=i(e,t.template)):t.base?(r=U,n=i(e,t.base)):t.list&&(r=j,n=i(e,t.list));const a={attributes:e};if(!r)return a;const s=r(n,t.segmentTimeline);if(n.duration){const{duration:e,timescale:t=1}=n;n.duration=e/t}else s.length?n.duration=s.reduce(((e,t)=>Math.max(e,Math.ceil(t.duration))),0):n.duration=0;return a.attributes=n,a.segments=s,t.base&&n.indexRange&&(a.sidx=s[0],a.segments=[]),a},H=e=>e.map(k),W=(e,t)=>s(e.childNodes).filter((({tagName:e})=>e===t)),V=e=>e.textContent.trim(),X=e=>{const t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;const[n,i,r,a,s,o]=t.slice(1);return 31536e3*parseFloat(n||0)+2592e3*parseFloat(i||0)+86400*parseFloat(r||0)+3600*parseFloat(a||0)+60*parseFloat(s||0)+parseFloat(o||0)},Y={mediaPresentationDuration:e=>X(e),availabilityStartTime(e){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(t=e)&&(t+="Z"),Date.parse(t)/1e3;var t},minimumUpdatePeriod:e=>X(e),suggestedPresentationDelay:e=>X(e),type:e=>e,timeShiftBufferDepth:e=>X(e),start:e=>X(e),width:e=>parseInt(e,10),height:e=>parseInt(e,10),bandwidth:e=>parseInt(e,10),frameRate:e=>(e=>parseFloat(e.split("/").reduce(((e,t)=>e/t))))(e),startNumber:e=>parseInt(e,10),timescale:e=>parseInt(e,10),presentationTimeOffset:e=>parseInt(e,10),duration(e){const t=parseInt(e,10);return isNaN(t)?X(e):t},d:e=>parseInt(e,10),t:e=>parseInt(e,10),r:e=>parseInt(e,10),presentationTime:e=>parseInt(e,10),DEFAULT:e=>e},Z=e=>e&&e.attributes?s(e.attributes).reduce(((e,t)=>{const n=Y[t.name]||Y.DEFAULT;return e[t.name]=n(t.value),e}),{}):{};function K(e){for(var t,n=(t=e,window.atob?window.atob(t):Buffer.from(t,"base64").toString("binary")),i=new Uint8Array(n.length),r=0;r<n.length;r++)i[r]=n.charCodeAt(r);return i}const J={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime","urn:mpeg:dash:mp4protection:2011":"mp4protection"},Q=(e,t)=>t.length?a(e.map((function(e){return t.map((function(t){const n=V(t),r=h(e.baseUrl,n),a=i(Z(t),{baseUrl:r});return r!==n&&!a.serviceLocation&&e.serviceLocation&&(a.serviceLocation=e.serviceLocation),a}))}))):e,ee=e=>{const t=W(e,"SegmentTemplate")[0],n=W(e,"SegmentList")[0],r=n&&W(n,"SegmentURL").map((e=>i({tag:"SegmentURL"},Z(e)))),a=W(e,"SegmentBase")[0],s=n||t,o=s&&W(s,"SegmentTimeline")[0],u=n||a||t,c=u&&W(u,"Initialization")[0],l=t&&Z(t);l&&c?l.initialization=c&&Z(c):l&&l.initialization&&(l.initialization={sourceURL:l.initialization});const d={template:l,segmentTimeline:o&&W(o,"S").map((e=>Z(e))),list:n&&i(Z(n),{segmentUrls:r,initialization:Z(c)}),base:a&&i(Z(a),{initialization:Z(c)})};return Object.keys(d).forEach((e=>{d[e]||delete d[e]})),d},te=e=>a(W(e.node,"EventStream").map((t=>{const n=Z(t),i=n.schemeIdUri;return W(t,"Event").map((t=>{const r=Z(t),a=r.presentationTime||0,s=n.timescale||1,o=r.duration||0,u=a/s+e.attributes.start;return{schemeIdUri:i,value:n.value,id:r.id,start:u,end:u+o/s,messageData:V(t)||r.messageData,contentEncoding:n.contentEncoding,presentationTimeOffset:n.presentationTimeOffset||0}}))}))),ne=(e,t,n)=>r=>{const s=Z(r),o=Q(t,W(r,"BaseURL")),u=W(r,"Role")[0],c={role:Z(u)};let l=i(e,s,c);const d=W(r,"Accessibility")[0],m=(e=>{if("urn:scte:dash:cc:cea-608:2015"===e.schemeIdUri)return("string"!=typeof e.value?[]:e.value.split(";")).map((e=>{let t,n;return n=e,/^CC\d=/.test(e)?[t,n]=e.split("="):/^CC\d$/.test(e)&&(t=e),{channel:t,language:n}}));if("urn:scte:dash:cc:cea-708:2015"===e.schemeIdUri)return("string"!=typeof e.value?[]:e.value.split(";")).map((e=>{const t={channel:void 0,language:void 0,aspectRatio:1,easyReader:0,"3D":0};if(/=/.test(e)){const[n,i=""]=e.split("=");t.channel=n,t.language=e,i.split(",").forEach((e=>{const[n,i]=e.split(":");"lang"===n?t.language=i:"er"===n?t.easyReader=Number(i):"war"===n?t.aspectRatio=Number(i):"3D"===n&&(t["3D"]=Number(i))}))}else t.language=e;return t.channel&&(t.channel="SERVICE"+t.channel),t}))})(Z(d));m&&(l=i(l,{captionServices:m}));const p=W(r,"Label")[0];if(p&&p.childNodes.length){const e=p.childNodes[0].nodeValue.trim();l=i(l,{label:e})}const f=W(r,"ContentProtection").reduce(((e,t)=>{const n=Z(t);n.schemeIdUri&&(n.schemeIdUri=n.schemeIdUri.toLowerCase());const i=J[n.schemeIdUri];if(i){e[i]={attributes:n};const r=W(t,"cenc:pssh")[0];if(r){const t=V(r);e[i].pssh=t&&K(t)}}return e}),{});Object.keys(f).length&&(l=i(l,{contentProtection:f}));const g=ee(r),h=W(r,"Representation"),b=i(n,g);return a(h.map(((e,t,n)=>r=>{const a=W(r,"BaseURL"),s=Q(t,a),o=i(e,Z(r)),u=ee(r);return s.map((e=>({segmentInfo:i(n,u),attributes:i(o,e)})))})(l,o,b)))},ie=(e,t)=>(n,r)=>{const s=Q(t,W(n.node,"BaseURL")),o=i(e,{periodStart:n.attributes.start});"number"==typeof n.attributes.duration&&(o.periodDuration=n.attributes.duration);const u=W(n.node,"AdaptationSet"),c=ee(n.node);return a(u.map(ne(o,s,c)))},re=(e,t)=>{if(e.length>1&&t({type:"warn",message:"The MPD manifest should contain no more than one ContentSteering tag"}),!e.length)return null;const n=i({serverURL:V(e[0])},Z(e[0]));return n.queryBeforeStart="true"===n.queryBeforeStart,n},ae=(e,t={})=>{const{manifestUri:n="",NOW:i=Date.now(),clientOffset:r=0,eventHandler:s=function(){}}=t,u=W(e,"Period");if(!u.length)throw new Error(o);const c=W(e,"Location"),l=Z(e),d=Q([{baseUrl:n}],W(e,"BaseURL")),m=W(e,"ContentSteering");l.type=l.type||"static",l.sourceDuration=l.mediaPresentationDuration||0,l.NOW=i,l.clientOffset=r,c.length&&(l.locations=c.map(V));const p=[];return u.forEach(((e,t)=>{const n=Z(e),i=p[t-1];n.start=(({attributes:e,priorPeriodAttributes:t,mpdType:n})=>"number"==typeof e.start?e.start:t&&"number"==typeof t.start&&"number"==typeof t.duration?t.start+t.duration:t||"static"!==n?null:0)({attributes:n,priorPeriodAttributes:i?i.attributes:null,mpdType:l.type}),p.push({node:e,attributes:n})})),{locations:l.locations,contentSteeringInfo:re(m,s),representationInfo:a(p.map(ie(l,d))),eventStream:a(p.map(te))}},se=e=>{if(""===e)throw new Error(u);const n=new t.DOMParser;let i,r;try{i=n.parseFromString(e,"application/xml"),r=i&&"MPD"===i.documentElement.tagName?i.documentElement:null}catch(e){}if(!r||r&&r.getElementsByTagName("parsererror").length>0)throw new Error(c);return r};e.VERSION="1.3.1",e.addSidxSegmentsToPlaylist=I,e.generateSidxKey=R,e.inheritAttributes=ae,e.parse=(e,t={})=>{const n=ae(se(e),t),i=H(n.representationInfo);return C({dashPlaylists:i,locations:n.locations,contentSteering:n.contentSteeringInfo,sidxMapping:t.sidxMapping,previousManifest:t.previousManifest,eventStream:n.eventStream})},e.parseUTCTiming=e=>(e=>{const t=W(e,"UTCTiming")[0];if(!t)return null;const n=Z(t);switch(n.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":n.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":n.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":n.method="DIRECT",n.value=Date.parse(n.value);break;default:throw new Error(m)}return n})(se(e)),e.stringToMpdXml=se,e.toM3u8=C,e.toPlaylists=H,Object.defineProperty(e,"__esModule",{value:!0})}));
