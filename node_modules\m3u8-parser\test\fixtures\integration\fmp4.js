module.exports = {
  allowCache: true,
  dateRanges: [],
  iFramePlaylists: [],
  mediaSequence: 1,
  playlistType: 'VOD',
  targetDuration: 6,
  discontinuitySequence: 0,
  discontinuityStarts: [],
  segments: [
    {
      byterange: {
        length: 5666510,
        offset: 720
      },
      duration: 6.006,
      timeline: 0,
      uri: 'main.mp4',
      map: {
        byterange: {
          length: 720,
          offset: 0
        },
        uri: 'main.mp4'
      }
    },
    {
      byterange: {
        length: 5861577,
        offset: 5667230
      },
      duration: 6.006,
      timeline: 0,
      uri: 'main.mp4',
      map: {
        byterange: {
          length: 720,
          offset: 0
        },
        uri: 'main.mp4'
      }
    }
  ],
  endList: true,
  version: 7,
  independentSegments: true
};
